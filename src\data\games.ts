// Game data for the dashboard
export interface Game {
  id: number;
  title: string;
  description: string;
  category: string;
  image: string;
  rating: number;
  plays: number;
  featured: boolean;
  tags: string[];
  difficulty: string;
}

export const games: Game[] = [
  {
    id: 1,
    title: "Snake Adventure",
    description: "Classic snake game with modern graphics and power-ups",
    category: "Arcade",
    image: "/api/placeholder/300/200",
    rating: 4.5,
    plays: 15420,
    featured: true,
    tags: ["Classic", "Arcade", "Retro"],
    difficulty: "Easy"
  },
  {
    id: 2,
    title: "Tetris Master",
    description: "The ultimate block puzzle experience with stunning visuals",
    category: "Puzzle",
    image: "/api/placeholder/300/200",
    rating: 4.8,
    plays: 23150,
    featured: true,
    tags: ["Puzzle", "Classic", "Strategy"],
    difficulty: "Medium"
  },
  {
    id: 3,
    title: "Space Invaders Redux",
    description: "Defend Earth from alien invasion in this retro shooter",
    category: "Shooter",
    image: "/api/placeholder/300/200",
    rating: 4.3,
    plays: 18750,
    featured: false,
    tags: ["Shooter", "Retro", "Action"],
    difficulty: "Medium"
  },
  {
    id: 4,
    title: "Pong Championship",
    description: "The classic paddle game that started it all",
    category: "Sports",
    image: "/api/placeholder/300/200",
    rating: 4.0,
    plays: 9320,
    featured: false,
    tags: ["Classic", "Sports", "Multiplayer"],
    difficulty: "Easy"
  },
  {
    id: 5,
    title: "Breakout Deluxe",
    description: "Break bricks and collect power-ups in this enhanced classic",
    category: "Arcade",
    image: "/api/placeholder/300/200",
    rating: 4.6,
    plays: 12890,
    featured: true,
    tags: ["Arcade", "Classic", "Power-ups"],
    difficulty: "Medium"
  },
  {
    id: 6,
    title: "Pac-Man Adventure",
    description: "Navigate mazes and collect dots while avoiding ghosts",
    category: "Arcade",
    image: "/api/placeholder/300/200",
    rating: 4.7,
    plays: 21340,
    featured: true,
    tags: ["Arcade", "Classic", "Maze"],
    difficulty: "Medium"
  },
  {
    id: 7,
    title: "Frogger Crossing",
    description: "Help the frog cross busy roads and rivers safely",
    category: "Arcade",
    image: "/api/placeholder/300/200",
    rating: 4.2,
    plays: 8760,
    featured: false,
    tags: ["Arcade", "Classic", "Adventure"],
    difficulty: "Hard"
  },
  {
    id: 8,
    title: "Asteroids Blast",
    description: "Pilot your ship through dangerous asteroid fields",
    category: "Shooter",
    image: "/api/placeholder/300/200",
    rating: 4.4,
    plays: 14520,
    featured: false,
    tags: ["Shooter", "Space", "Action"],
    difficulty: "Hard"
  },
  {
    id: 9,
    title: "Centipede Strike",
    description: "Shoot the centipede before it reaches the bottom",
    category: "Shooter",
    image: "/api/placeholder/300/200",
    rating: 4.1,
    plays: 7890,
    featured: false,
    tags: ["Shooter", "Classic", "Fast-paced"],
    difficulty: "Medium"
  },
  {
    id: 10,
    title: "Missile Command",
    description: "Defend your cities from incoming missile attacks",
    category: "Strategy",
    image: "/api/placeholder/300/200",
    rating: 4.3,
    plays: 11230,
    featured: false,
    tags: ["Strategy", "Defense", "Classic"],
    difficulty: "Hard"
  }
];

export const categories = [
  "All",
  "Arcade",
  "Puzzle", 
  "Shooter",
  "Sports",
  "Strategy"
];

export const getFeaturedGames = (): Game[] => games.filter(game => game.featured);
export const getGamesByCategory = (category: string): Game[] => 
  category === "All" ? games : games.filter(game => game.category === category);
export const getTopRatedGames = (): Game[] => [...games].sort((a, b) => b.rating - a.rating);
export const getMostPlayedGames = (): Game[] => [...games].sort((a, b) => b.plays - a.plays);
