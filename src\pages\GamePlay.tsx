import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { ArrowLeft, Pause, RotateCcw, Volume2, VolumeX } from 'lucide-react';
import { useState, useEffect } from 'react';
import { games } from '../data/games';

const GamePlay = () => {
  const { id } = useParams();
  const game = games.find(g => g.id === parseInt(id || '0'));
  const [isPaused, setIsPaused] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [score, setScore] = useState(0);
  const [gameStarted, setGameStarted] = useState(false);

  useEffect(() => {
    // Simulate score updates for demo
    if (gameStarted && !isPaused) {
      const interval = setInterval(() => {
        setScore(prev => prev + Math.floor(Math.random() * 10));
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [gameStarted, isPaused]);

  if (!game) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Game Not Found</h1>
          <Link to="/games" className="text-blue-400 hover:text-blue-300">
            ← Back to Games
          </Link>
        </div>
      </div>
    );
  }

  const handleStartGame = () => {
    setGameStarted(true);
    setScore(0);
  };

  const handlePauseGame = () => {
    setIsPaused(!isPaused);
  };

  const handleRestartGame = () => {
    setScore(0);
    setIsPaused(false);
    setGameStarted(true);
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Game Header */}
      <div className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link
                to={`/game/${game.id}`}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <ArrowLeft className="h-6 w-6" />
              </Link>
              <h1 className="text-xl font-semibold text-white">{game.title}</h1>
            </div>

            <div className="flex items-center space-x-4">
              <div className="text-white">
                Score: <span className="font-bold text-blue-400">{score.toLocaleString()}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={handlePauseGame}
                  disabled={!gameStarted}
                  className="bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white p-2 rounded transition-colors"
                >
                  <Pause className="h-4 w-4" />
                </button>
                
                <button
                  onClick={handleRestartGame}
                  className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded transition-colors"
                >
                  <RotateCcw className="h-4 w-4" />
                </button>
                
                <button
                  onClick={() => setIsMuted(!isMuted)}
                  className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded transition-colors"
                >
                  {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Game Container */}
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="bg-gray-800 rounded-lg shadow-2xl overflow-hidden max-w-4xl w-full">
          {/* Game Canvas Area */}
          <div className="relative bg-black aspect-video flex items-center justify-center">
            {!gameStarted ? (
              /* Game Start Screen */
              <div className="text-center">
                <img
                  src={`https://picsum.photos/200/150?random=${game.id}`}
                  alt={game.title}
                  className="w-32 h-24 object-cover rounded-lg mx-auto mb-4"
                />
                <h2 className="text-2xl font-bold text-white mb-2">{game.title}</h2>
                <p className="text-gray-400 mb-6 max-w-md">{game.description}</p>
                <button
                  onClick={handleStartGame}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-colors"
                >
                  Start Game
                </button>
                <div className="mt-4 text-sm text-gray-500">
                  Use arrow keys or WASD to play
                </div>
              </div>
            ) : isPaused ? (
              /* Pause Screen */
              <div className="text-center">
                <Pause className="h-16 w-16 text-white mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-white mb-4">Game Paused</h2>
                <button
                  onClick={handlePauseGame}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
                >
                  Resume
                </button>
              </div>
            ) : (
              /* Game Playing Screen - This would contain the actual game */
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-600 rounded-full mx-auto mb-4 animate-bounce"></div>
                  <h3 className="text-xl font-semibold text-white mb-2">Game Running</h3>
                  <p className="text-gray-400">This is where the actual {game.title} game would be rendered</p>
                  <div className="mt-4 text-sm text-gray-500">
                    Score: {score} | Use arrow keys to play
                  </div>
                </div>
              </div>
            )}

            {/* Game Status Overlay */}
            {gameStarted && !isPaused && (
              <div className="absolute top-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded">
                Playing...
              </div>
            )}
          </div>

          {/* Game Controls Info */}
          <div className="bg-gray-700 p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <h4 className="font-semibold text-white mb-2">Controls</h4>
                <ul className="text-gray-300 space-y-1">
                  <li>Arrow Keys: Move</li>
                  <li>Space: Action</li>
                  <li>ESC: Pause</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-white mb-2">Objective</h4>
                <p className="text-gray-300">
                  {game.category === 'Arcade' ? 'Get the highest score possible!' :
                   game.category === 'Puzzle' ? 'Solve the puzzle to advance!' :
                   'Complete the challenge!'}
                </p>
              </div>
              <div>
                <h4 className="font-semibold text-white mb-2">Tips</h4>
                <ul className="text-gray-300 space-y-1">
                  <li>Practice makes perfect</li>
                  <li>Watch for patterns</li>
                  <li>Stay focused</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Game Info Sidebar (Hidden on mobile) */}
      <div className="hidden lg:block fixed right-4 top-1/2 transform -translate-y-1/2 w-64">
        <div className="bg-gray-800 rounded-lg p-4">
          <h3 className="font-semibold text-white mb-3">Game Info</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-400">Difficulty:</span>
              <span className="text-white">{game.difficulty}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Category:</span>
              <span className="text-blue-400">{game.category}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Rating:</span>
              <span className="text-yellow-400">★ {game.rating}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Total Plays:</span>
              <span className="text-white">{game.plays.toLocaleString()}</span>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-700">
            <Link
              to={`/game/${game.id}`}
              className="block w-full bg-gray-700 hover:bg-gray-600 text-white text-center py-2 rounded transition-colors"
            >
              Game Details
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GamePlay;
