// News Page Functionality
class NewsPage {
    constructor() {
        this.articles = [];
        this.currentCategory = 'all';
        this.currentSort = 'newest';
        this.currentPage = 1;
        this.articlesPerPage = 6;
        this.loading = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadArticles();
        this.setupNewsletter();
    }

    setupEventListeners() {
        // Category filters
        const categoryBtns = document.querySelectorAll('.category-btn');
        categoryBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const category = e.target.dataset.category;
                this.filterByCategory(category);
            });
        });

        // Sort dropdown
        const sortSelect = document.getElementById('news-sort');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.sortArticles();
                this.renderArticles();
            });
        }

        // Load more button
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreArticles();
            });
        }

        // Featured article button
        const featuredBtn = document.querySelector('.featured-article .btn');
        if (featuredBtn) {
            featuredBtn.addEventListener('click', () => {
                this.showArticleModal('featured-article');
            });
        }
    }

    async loadArticles() {
        this.showLoading(true);
        
        try {
            // Try to load from Supabase first
            if (window.supabaseClient) {
                this.articles = await window.supabaseClient.getNewsArticles();
            } else {
                // Fallback to mock data
                this.articles = this.getMockArticles();
            }
            
            this.sortArticles();
            this.renderArticles();
        } catch (error) {
            console.error('Error loading articles:', error);
            this.articles = this.getMockArticles();
            this.renderArticles();
        } finally {
            this.showLoading(false);
        }
    }

    getMockArticles() {
        return [
            {
                id: '1',
                title: 'GameHub 2.0: Enhanced Gaming Experience',
                slug: 'gamehub-2-0-enhanced-gaming-experience',
                excerpt: 'We\'re excited to announce the launch of GameHub 2.0 with improved performance, new games, and enhanced user experience.',
                content: 'Full article content here...',
                category: 'updates',
                author_name: 'GameHub Team',
                published_at: '2024-12-15T10:00:00Z',
                view_count: 1250,
                like_count: 89,
                featured_image_url: null
            },
            {
                id: '2',
                title: 'New Leaderboard System Launch',
                slug: 'new-leaderboard-system-launch',
                excerpt: 'Our enhanced leaderboard system brings real-time rankings, tournaments, and competitive features to GameHub.',
                content: 'Full article content here...',
                category: 'updates',
                author_name: 'GameHub Team',
                published_at: '2024-12-10T14:30:00Z',
                view_count: 890,
                like_count: 67,
                featured_image_url: null
            },
            {
                id: '3',
                title: 'Community Tournament Results',
                slug: 'community-tournament-results',
                excerpt: 'Congratulations to all participants in our first community tournament! See the winners and highlights.',
                content: 'Full article content here...',
                category: 'community',
                author_name: 'GameHub Team',
                published_at: '2024-12-05T16:45:00Z',
                view_count: 654,
                like_count: 45,
                featured_image_url: null
            },
            {
                id: '4',
                title: 'New Game: Space Invaders Classic',
                slug: 'new-game-space-invaders-classic',
                excerpt: 'Experience the classic arcade game that started it all. Space Invaders is now available on GameHub!',
                content: 'Full article content here...',
                category: 'games',
                author_name: 'GameHub Team',
                published_at: '2024-11-28T12:00:00Z',
                view_count: 1100,
                like_count: 78,
                featured_image_url: null
            },
            {
                id: '5',
                title: 'Pro Tips: Mastering Snake Game',
                slug: 'pro-tips-mastering-snake-game',
                excerpt: 'Learn advanced strategies and techniques to achieve high scores in the classic Snake game.',
                content: 'Full article content here...',
                category: 'tips',
                author_name: 'Pro Gamer',
                published_at: '2024-11-25T09:15:00Z',
                view_count: 432,
                like_count: 34,
                featured_image_url: null
            },
            {
                id: '6',
                title: 'Holiday Gaming Event Announcement',
                slug: 'holiday-gaming-event-announcement',
                excerpt: 'Join us for special holiday challenges, exclusive rewards, and festive game modes throughout December!',
                content: 'Full article content here...',
                category: 'events',
                author_name: 'GameHub Team',
                published_at: '2024-11-20T11:30:00Z',
                view_count: 987,
                like_count: 123,
                featured_image_url: null
            }
        ];
    }

    filterByCategory(category) {
        this.currentCategory = category;
        this.currentPage = 1;
        
        // Update active button
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');
        
        this.renderArticles();
    }

    sortArticles() {
        switch (this.currentSort) {
            case 'newest':
                this.articles.sort((a, b) => new Date(b.published_at) - new Date(a.published_at));
                break;
            case 'oldest':
                this.articles.sort((a, b) => new Date(a.published_at) - new Date(b.published_at));
                break;
            case 'popular':
                this.articles.sort((a, b) => (b.view_count + b.like_count) - (a.view_count + a.like_count));
                break;
        }
    }

    getFilteredArticles() {
        if (this.currentCategory === 'all') {
            return this.articles;
        }
        return this.articles.filter(article => article.category === this.currentCategory);
    }

    renderArticles() {
        const newsGrid = document.getElementById('news-grid');
        if (!newsGrid) return;

        const filteredArticles = this.getFilteredArticles();
        const startIndex = (this.currentPage - 1) * this.articlesPerPage;
        const endIndex = startIndex + this.articlesPerPage;
        const articlesToShow = filteredArticles.slice(0, endIndex);

        if (articlesToShow.length === 0) {
            newsGrid.innerHTML = `
                <div class="search-no-results">
                    <h3>No articles found</h3>
                    <p>Try selecting a different category or check back later for new content.</p>
                </div>
            `;
            return;
        }

        newsGrid.innerHTML = articlesToShow.map(article => this.createArticleCard(article)).join('');
        
        // Update load more button
        this.updateLoadMoreButton(filteredArticles.length, endIndex);
        
        // Add click listeners to article cards
        this.setupArticleClickListeners();
    }

    createArticleCard(article) {
        const publishDate = new Date(article.published_at);
        const formattedDate = publishDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });

        const categoryColors = {
            updates: '#00d4ff',
            games: '#10b981',
            community: '#f59e0b',
            events: '#ef4444',
            tips: '#8b5cf6'
        };

        return `
            <article class="news-card" data-article-id="${article.id}">
                <div class="news-image">
                    <span class="placeholder-icon">${this.getCategoryIcon(article.category)}</span>
                    <div class="news-date-overlay">${formattedDate}</div>
                </div>
                <div class="news-content">
                    <div class="news-category" style="color: ${categoryColors[article.category] || '#00d4ff'}">
                        ${article.category.charAt(0).toUpperCase() + article.category.slice(1)}
                    </div>
                    <h3 class="news-title">${article.title}</h3>
                    <p class="news-excerpt">${article.excerpt}</p>
                    <div class="news-meta">
                        <span class="news-author">By ${article.author_name}</span>
                        <button type="button" class="read-more-btn" data-article-id="${article.id}">
                            Read More →
                        </button>
                    </div>
                </div>
            </article>
        `;
    }

    getCategoryIcon(category) {
        const icons = {
            updates: '🚀',
            games: '🎮',
            community: '👥',
            events: '🎉',
            tips: '💡'
        };
        return icons[category] || '📰';
    }

    setupArticleClickListeners() {
        const readMoreBtns = document.querySelectorAll('.read-more-btn');
        readMoreBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const articleId = e.target.dataset.articleId;
                this.showArticleModal(articleId);
            });
        });

        const articleCards = document.querySelectorAll('.news-card');
        articleCards.forEach(card => {
            card.addEventListener('click', () => {
                const articleId = card.dataset.articleId;
                this.showArticleModal(articleId);
            });
        });
    }

    showArticleModal(articleId) {
        const article = this.articles.find(a => a.id === articleId);
        if (!article) return;

        // Create modal
        const modal = document.createElement('div');
        modal.className = 'article-modal';
        modal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h2>${article.title}</h2>
                    <button type="button" class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="article-meta">
                        <span class="article-category">${article.category}</span>
                        <span class="article-author">By ${article.author_name}</span>
                        <span class="article-date">${new Date(article.published_at).toLocaleDateString()}</span>
                    </div>
                    <div class="article-content">
                        <p>${article.content || article.excerpt}</p>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Add event listeners
        const closeBtn = modal.querySelector('.modal-close');
        const overlay = modal.querySelector('.modal-overlay');
        
        const closeModal = () => {
            modal.remove();
        };

        closeBtn.addEventListener('click', closeModal);
        overlay.addEventListener('click', closeModal);
        
        // Close on escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);

        // Track view
        this.trackArticleView(articleId);
    }

    updateLoadMoreButton(totalArticles, currentlyShown) {
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (!loadMoreBtn) return;

        if (currentlyShown >= totalArticles) {
            loadMoreBtn.style.display = 'none';
        } else {
            loadMoreBtn.style.display = 'block';
            loadMoreBtn.textContent = `Load More Articles (${totalArticles - currentlyShown} remaining)`;
        }
    }

    loadMoreArticles() {
        this.currentPage++;
        this.renderArticles();
    }

    setupNewsletter() {
        const newsletterForm = document.getElementById('newsletter-form');
        if (!newsletterForm) return;

        newsletterForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const emailInput = newsletterForm.querySelector('input[type="email"]');
            const email = emailInput.value.trim();
            
            if (!email) return;

            const submitBtn = newsletterForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            try {
                submitBtn.textContent = 'Subscribing...';
                submitBtn.disabled = true;
                
                // Simulate API call
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Show success message
                this.showNotification('Successfully subscribed to newsletter!', 'success');
                emailInput.value = '';
                
            } catch (error) {
                console.error('Newsletter subscription error:', error);
                this.showNotification('Failed to subscribe. Please try again.', 'error');
            } finally {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    }

    showLoading(show) {
        const loadingState = document.getElementById('news-loading');
        const newsGrid = document.getElementById('news-grid');
        
        if (loadingState) {
            loadingState.style.display = show ? 'block' : 'none';
        }
        if (newsGrid) {
            newsGrid.style.display = show ? 'none' : 'grid';
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button type="button" class="notification-close">&times;</button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);

        // Close button
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });
    }

    trackArticleView(articleId) {
        // Track article view for analytics
        if (window.supabaseClient) {
            // This would increment view count in database
            console.log(`Tracking view for article: ${articleId}`);
        }
    }
}

// Initialize news page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.body.contains(document.querySelector('.news-grid'))) {
        window.newsPage = new NewsPage();
    }
});
