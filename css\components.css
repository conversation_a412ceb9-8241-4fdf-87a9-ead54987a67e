/* Navigation Components */
.navbar {
    background: rgba(var(--primary-bg-rgb, 15, 20, 25), 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 1px solid var(--border-color);
    transition: all var(--transition-medium);
}

.navbar.scrolled {
    background: rgba(var(--primary-bg-rgb, 15, 20, 25), 0.98);
    box-shadow: 0 2px 20px var(--shadow-medium);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 20px;
    padding-right: 20px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
    text-decoration: none;
    transition: transform var(--transition-fast);
}

.nav-brand:hover {
    transform: scale(1.05);
}

.nav-logo {
    font-size: 2rem;
    animation: pulse 2s infinite;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
    align-items: center;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-fast);
    position: relative;
    padding: 8px 16px;
    border-radius: var(--border-radius-md);
}

.nav-link:hover {
    color: var(--accent-primary);
    background: var(--tertiary-bg);
    transform: translateY(-1px);
}

.nav-link.active {
    color: var(--accent-primary);
    background: var(--tertiary-bg);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: var(--accent-primary);
    border-radius: 1px;
}

/* Navigation Dropdown */
.nav-dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 8px 0;
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    box-shadow: 0 10px 25px var(--shadow-medium);
    z-index: 1001;
}

.nav-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: 12px 20px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
    font-size: 0.9rem;
}

.dropdown-link:hover,
.dropdown-link.active {
    background: var(--tertiary-bg);
    color: var(--accent-primary);
}

/* Navigation Actions */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.theme-toggle {
    background: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: 8px 12px;
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 1.2rem;
}

.theme-toggle:hover {
    background: var(--accent-primary);
    color: var(--primary-bg);
    transform: scale(1.1);
}

.social-links {
    display: flex;
    gap: 8px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: var(--tertiary-bg);
    border-radius: var(--border-radius-md);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
    font-size: 1.1rem;
}

.social-links a:hover {
    background: var(--accent-primary);
    color: var(--primary-bg);
    transform: translateY(-2px);
}

/* Mobile Navigation Toggle */
.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle .bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: all var(--transition-fast);
    border-radius: 2px;
}

.nav-toggle.active .bar:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active .bar:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Breadcrumb Navigation */
.breadcrumb {
    background: var(--secondary-bg);
    padding: 15px 0;
    margin-top: 70px;
    border-bottom: 1px solid var(--border-color);
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    list-style: none;
    gap: 8px;
    font-size: 0.9rem;
}

.breadcrumb-list li {
    display: flex;
    align-items: center;
}

.breadcrumb-list li:not(:last-child)::after {
    content: '›';
    margin-left: 8px;
    color: var(--text-muted);
}

.breadcrumb-list a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.breadcrumb-list a:hover {
    color: var(--accent-primary);
}

.breadcrumb-list li[aria-current="page"] {
    color: var(--text-primary);
    font-weight: 500;
}

/* Button Components */
.btn {
    padding: 15px 30px;
    border: none;
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--primary-bg);
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--accent-primary);
    border: 2px solid var(--accent-primary);
}

.btn-secondary:hover {
    background: var(--accent-primary);
    color: var(--primary-bg);
    transform: translateY(-2px);
}

.btn-danger {
    background: var(--error);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-2px);
}

.btn-icon {
    font-size: 1.1em;
}

/* Card Components */
.card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.card:hover::before {
    left: 100%;
}

.card:hover {
    transform: translateY(-3px);
    border-color: var(--accent-primary);
    box-shadow: 0 10px 25px var(--shadow-medium);
}

/* Loading States */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    color: var(--text-primary);
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.empty-state p {
    margin-bottom: 30px;
    line-height: 1.6;
}

/* Notification Components */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 16px 20px;
    max-width: 400px;
    z-index: 3000;
    box-shadow: 0 10px 25px var(--shadow-medium);
    animation: slideInRight 0.3s ease;
}

.notification-success {
    border-color: var(--success);
    background: rgba(16, 185, 129, 0.1);
}

.notification-error {
    border-color: var(--error);
    background: rgba(239, 68, 68, 0.1);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-icon {
    font-size: 1.2rem;
}

.notification-message {
    flex: 1;
    color: var(--text-primary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1.2rem;
    padding: 0;
    margin-left: 10px;
    transition: color var(--transition-fast);
}

.notification-close:hover {
    color: var(--text-primary);
}

/* Footer Component */
.footer {
    background: var(--secondary-bg);
    border-top: 1px solid var(--border-color);
    padding: 60px 0 30px;
    margin-top: 80px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    color: var(--accent-primary);
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.footer-section p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--accent-primary);
}

.social-links-footer {
    display: flex;
    gap: 15px;
}

.social-links-footer a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--tertiary-bg);
    border-radius: var(--border-radius-md);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
    font-size: 1.2rem;
}

.social-links-footer a:hover {
    background: var(--accent-primary);
    color: var(--primary-bg);
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--secondary-bg);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 50px;
        transition: left var(--transition-medium);
        border-right: 1px solid var(--border-color);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background: transparent;
        padding: 0;
        margin-top: 10px;
    }

    .dropdown-menu.active {
        display: block;
    }

    .nav-actions {
        gap: 10px;
    }

    .social-links {
        gap: 5px;
    }

    .social-links a {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }

    .notification {
        left: 20px;
        right: 20px;
        max-width: none;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .social-links-footer {
        justify-content: center;
    }
}
