<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GameHub - Browser Games Dashboard</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <span class="nav-logo">🎮</span>
                <span class="nav-title">GameHub</span>
            </div>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link active" data-page="home">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#games" class="nav-link" data-page="games">Games</a>
                </li>
                <li class="nav-item">
                    <a href="#rankings" class="nav-link" data-page="rankings">Rankings</a>
                </li>
                <li class="nav-item">
                    <a href="#news" class="nav-link" data-page="news">News</a>
                </li>
            </ul>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Home Page -->
        <section id="home" class="page active">
            <!-- Hero Section -->
            <div class="hero">
                <div class="hero-content">
                    <h1 class="hero-title">Welcome to GameHub</h1>
                    <p class="hero-subtitle">Discover amazing browser-based 2D games! Play classic arcade games, compete with friends, and enjoy endless entertainment.</p>
                    <div class="hero-buttons">
                        <button class="btn btn-primary" onclick="showPage('games')">Start Playing</button>
                        <button class="btn btn-secondary" onclick="showPage('rankings')">View Rankings</button>
                    </div>
                </div>
            </div>

            <!-- Featured Games -->
            <div class="section">
                <div class="container">
                    <h2 class="section-title">Featured Games</h2>
                    <div class="games-grid" id="featured-games">
                        <!-- Games will be loaded here by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Latest News -->
            <div class="section bg-dark">
                <div class="container">
                    <h2 class="section-title">Latest News</h2>
                    <div class="news-grid" id="latest-news">
                        <!-- News will be loaded here by JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Games Page -->
        <section id="games" class="page">
            <div class="container">
                <h1 class="page-title">All Games</h1>
                
                <!-- Filters -->
                <div class="filters">
                    <div class="search-box">
                        <input type="text" id="search-input" placeholder="Search games..." class="search-input">
                    </div> 
                    <div class="filter-controls">
                        <select id="category-filter" class="filter-select">
                            <option value="all" title="Filter games by category">All Categories</option>
                            <option value="arcade">Arcade</option>
                            <option value="puzzle">Puzzle</option>
                            <option value="action">Action</option>
                            <option value="strategy">Strategy</option>
 </select>
                        <select id="sort-filter" class="filter-select">
                        <select id="sort-filter" class="filter-select">
                            <option value="name">Sort by Name</option>
                            <option value="rating">Sort by Rating</option>
                            <option value="plays">Sort by Popularity</option>
                        </select>
                    </div>
                </div>

                <!-- Games Grid -->
                <div class="games-grid" id="all-games">
                    <!-- Games will be loaded here by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Rankings Page -->
        <section id="rankings" class="page">
            <div class="container">
                <h1 class="page-title">Game Rankings</h1>
                
                <!-- Ranking Tabs -->
                <div class="tabs">
                    <button class="tab-btn active" data-tab="top-rated">Top Rated</button>
                    <button class="tab-btn" data-tab="most-played">Most Played</button>
                </div>

                <!-- Rankings Content -->
                <div class="rankings-content" id="rankings-content">
                    <!-- Rankings will be loaded here by JavaScript -->
                </div>
            </div>
        </section>

        <!-- News Page -->
        <section id="news" class="page">
            <div class="container">
                <h1 class="page-title">Latest News</h1>
                
                <!-- News Categories -->
                <div class="news-categories">
                    <button class="category-btn active" data-category="all">All</button>
                    <button class="category-btn" data-category="updates">Game Updates</button>
                    <button class="category-btn" data-category="events">Events</button>
                    <button class="category-btn" data-category="features">New Features</button>
                </div>

                <!-- News Grid -->
                <div class="news-grid" id="news-content">
                    <!-- News will be loaded here by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Game Detail Modal -->
        <div id="game-modal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeGameModal()">&times;</span>
                <div id="game-detail-content">
                    <!-- Game details will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Game Play Modal -->
        <div id="game-play-modal" class="modal">
            <div class="modal-content game-play-content">
                <div class="game-header">
                    <h3 id="game-play-title">Game Title</h3>
                    <div class="game-controls">
                        <button id="pause-btn" class="control-btn">⏸️</button>
                        <button id="restart-btn" class="control-btn">🔄</button>
                        <button class="control-btn" onclick="closeGamePlayModal()">❌</button>
                    </div>
                </div>
                <div class="game-container">
                    <canvas id="game-canvas" width="800" height="600"></canvas>
                    <div id="game-ui">
                        <div class="score">Score: <span id="score">0</span></div>
                        <div class="level">Level: <span id="level">1</span></div>
                    </div>
                </div>
                <div class="game-instructions" id="game-instructions">
                    <!-- Game instructions will be loaded here -->
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>GameHub</h3>
                    <p>Your ultimate destination for browser-based 2D games.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#home" onclick="showPage('home')">Home</a></li>
                        <li><a href="#games" onclick="showPage('games')">Games</a></li>
                        <li><a href="#rankings" onclick="showPage('rankings')">Rankings</a></li>
                        <li><a href="#news" onclick="showPage('news')">News</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 GameHub. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/data.js"></script>
    <script src="js/games/snake.js"></script>
    <script src="js/games/tetris.js"></script>
    <script src="js/games/pong.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
