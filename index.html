<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="GameHub - The ultimate destination for browser-based 2D games. Play classic arcade games, compete with friends, and discover new favorites.">
    <meta name="keywords" content="browser games, 2D games, arcade games, online games, free games, snake, tetris, pong">
    <meta name="author" content="GameHub Team">
    <meta property="og:title" content="GameHub - Browser Games Dashboard">
    <meta property="og:description" content="Discover amazing browser-based 2D games! Play classic arcade games and compete with friends.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://gamehub.example.com">
    <title>GameHub - Browser Games Dashboard | Play Free 2D Games Online</title>
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/pages.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <span class="nav-logo">🎮</span>
                <span class="nav-title">GameHub</span>
            </div>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link active">Home</a>
                </li>
                <li class="nav-item">
                    <a href="games.html" class="nav-link">Games</a>
                </li>
                <li class="nav-item">
                    <a href="leaderboards.html" class="nav-link">Leaderboards</a>
                </li>
                <li class="nav-item">
                    <a href="news.html" class="nav-link">News</a>
                </li>
                <li class="nav-item nav-dropdown">
                    <a href="#" class="nav-link" id="more-dropdown">More ▼</a>
                    <ul class="dropdown-menu" id="dropdown-menu">
                        <li><a href="about.html" class="dropdown-link">About</a></li>
                        <li><a href="contact.html" class="dropdown-link">Contact</a></li>
                        <li><a href="help.html" class="dropdown-link">Help</a></li>
                        <li><a href="profile.html" class="dropdown-link">Profile</a></li>
                        <li><a href="settings.html" class="dropdown-link">Settings</a></li>
                    </ul>
                </li>
            </ul>
            <div class="nav-actions">
                <button type="button" class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                    <span class="theme-icon">🌙</span>
                </button>
                <div class="social-links">
                    <a href="https://discord.gg/gamehub" target="_blank" rel="noopener" aria-label="Join Discord">
                        <span class="social-icon">💬</span>
                    </a>
                    <a href="https://github.com/gamehub" target="_blank" rel="noopener" aria-label="GitHub">
                        <span class="social-icon">⚡</span>
                    </a>
                </div>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Home Page -->
        <section id="home" class="page active">
            <!-- Hero Section -->
            <div class="hero">
                <div class="hero-content">
                    <div class="hero-animation">
                        <div class="floating-icons">
                            <span class="float-icon">🎮</span>
                            <span class="float-icon">🏆</span>
                            <span class="float-icon">⭐</span>
                            <span class="float-icon">🎯</span>
                        </div>
                    </div>
                    <h1 class="hero-title">Welcome to GameHub</h1>
                    <p class="hero-subtitle">Discover amazing browser-based 2D games! Play classic arcade games, compete with friends, and enjoy endless entertainment.</p>
                    <div class="hero-buttons">
                        <button type="button" class="btn btn-primary" onclick="showPage('games')">Start Playing</button>
                        <button type="button" class="btn btn-secondary" onclick="showPage('rankings')">View Rankings</button>
                    </div>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <span class="stat-number" id="total-games">8</span>
                            <span class="stat-label">Games Available</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="total-players">1.2k</span>
                            <span class="stat-label">Active Players</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="total-scores">50k</span>
                            <span class="stat-label">High Scores</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Stats Dashboard -->
            <div class="section">
                <div class="container">
                    <h2 class="section-title">Your Gaming Dashboard</h2>
                    <div class="quick-stats" id="quick-stats">
                        <div class="stat-card">
                            <div class="stat-icon">🎮</div>
                            <div class="stat-info">
                                <h3>Games Played</h3>
                                <p class="stat-value" id="user-games-played">0</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🏆</div>
                            <div class="stat-info">
                                <h3>Best Score</h3>
                                <p class="stat-value" id="user-best-score">0</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">⏱️</div>
                            <div class="stat-info">
                                <h3>Time Played</h3>
                                <p class="stat-value" id="user-time-played">0h</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🎯</div>
                            <div class="stat-info">
                                <h3>Achievements</h3>
                                <p class="stat-value" id="user-achievements">0</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recently Played Games -->
            <div class="section bg-dark">
                <div class="container">
                    <h2 class="section-title">Recently Played</h2>
                    <div class="recently-played" id="recently-played">
                        <p class="empty-state">No games played yet. Start playing to see your recent games here!</p>
                    </div>
                </div>
            </div>

            <!-- Featured Games Carousel -->
            <div class="section">
                <div class="container">
                    <h2 class="section-title">Featured Games</h2>
                    <div class="game-carousel">
                        <button type="button" class="carousel-btn prev" id="carousel-prev" aria-label="Previous games">‹</button>
                        <div class="carousel-container">
                            <div class="games-carousel" id="featured-games">
                                <!-- Games will be loaded here by JavaScript -->
                            </div>
                        </div>
                        <button type="button" class="carousel-btn next" id="carousel-next" aria-label="Next games">›</button>
                    </div>
                </div>
            </div>

            <!-- Testimonials -->
            <div class="section bg-dark">
                <div class="container">
                    <h2 class="section-title">What Players Say</h2>
                    <div class="testimonials">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <span class="quote-icon">“</span>
                                <p>"GameHub has the best collection of classic games! I love the smooth gameplay and modern design."</p>
                            </div>
                            <div class="testimonial-author">
                                <div class="author-avatar">👤</div>
                                <div class="author-info">
                                    <h4>Alex Chen</h4>
                                    <span>Pro Gamer</span>
                                </div>
                            </div>
                        </div>
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <span class="quote-icon">“</span>
                                <p>"Perfect for quick gaming sessions. The leaderboards keep me coming back for more!"</p>
                            </div>
                            <div class="testimonial-author">
                                <div class="author-avatar">👩</div>
                                <div class="author-info">
                                    <h4>Sarah Johnson</h4>
                                    <span>Casual Player</span>
                                </div>
                            </div>
                        </div>
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <span class="quote-icon">“</span>
                                <p>"Nostalgic games with a modern twist. Great way to relax during breaks!"</p>
                            </div>
                            <div class="testimonial-author">
                                <div class="author-avatar">👨</div>
                                <div class="author-info">
                                    <h4>Mike Rodriguez</h4>
                                    <span>Developer</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Newsletter Signup -->
            <div class="section">
                <div class="container">
                    <div class="newsletter-signup">
                        <div class="newsletter-content">
                            <h2>Stay Updated</h2>
                            <p>Get notified about new games, tournaments, and exclusive content!</p>
                        </div>
                        <form class="newsletter-form" id="newsletter-form">
                            <input type="email" placeholder="Enter your email" class="newsletter-input" required aria-label="Email address">
                            <button type="submit" class="btn btn-primary">Subscribe</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Latest News -->
            <div class="section bg-dark">
                <div class="container">
                    <h2 class="section-title">Latest News</h2>
                    <div class="news-grid" id="latest-news">
                        <!-- News will be loaded here by JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Games Page -->
        <section id="games" class="page">
            <div class="container">
                <h1 class="page-title">All Games</h1>

                <!-- Filters -->
                <div class="filters">
                    <div class="search-box">
                        <input type="text" id="search-input" placeholder="Search games..." class="search-input">
                    </div>
                    <div class="filter-controls">
                        <select id="category-filter" class="filter-select">
                            <option value="all" title="Filter games by category">All Categories</option>
                            <option value="arcade">Arcade</option>
                            <option value="action">Action</option>
                            <option value="strategy">Strategy</option>
                        </select>
                        <select id="sort-filter" class="filter-select">
                            <option value="name">Sort by Name</option>
                            <option value="rating" title="Sort games by rating">Sort by Rating</option>
                            <option value="plays">Sort by Popularity</option>
                        </select>
                    </div>
                </div>

                <!-- Games Grid -->
                <div class="games-grid" id="all-games">
                    <!-- Games will be loaded here by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Rankings Page -->
        <section id="rankings" class="page">
            <div class="container">
                <h1 class="page-title">Game Rankings</h1>

                <!-- Ranking Tabs -->
                <div class="tabs">
                    <button class="tab-btn active" data-tab="top-rated">Top Rated</button>
                    <button class="tab-btn" data-tab="most-played">Most Played</button>
                </div>

                <!-- Rankings Content -->
                <div class="rankings-content" id="rankings-content">
                    <!-- Rankings will be loaded here by JavaScript -->
                </div>
            </div>
        </section>

        <!-- News Page -->
        <section id="news" class="page">
            <div class="container">
                <h1 class="page-title">Latest News</h1>

                <!-- News Categories -->
                <div class="news-categories">
                    <button class="category-btn active" data-category="all">All</button>
                    <button class="category-btn" data-category="updates">Game Updates</button>
                    <button class="category-btn" data-category="events">Events</button>
                    <button class="category-btn" data-category="features">New Features</button>
                </div>

                <!-- News Grid -->
                <div class="news-grid" id="news-content">
                    <!-- News will be loaded here by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Game Detail Modal -->
        <div id="game-modal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeGameModal()">&times;</span>
                <div id="game-detail-content">
                    <!-- Game details will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Game Play Modal -->
        <div id="game-play-modal" class="modal">
            <div class="modal-content game-play-content">
                <div class="game-header">
                    <h3 id="game-play-title">Game Title</h3>
                    <div class="game-controls">
                        <button id="pause-btn" class="control-btn">⏸️</button>
                        <button id="restart-btn" class="control-btn">🔄</button>
                        <button class="control-btn" onclick="closeGamePlayModal()">❌</button>
                    </div>
                </div>
                <div class="game-container">
                    <canvas id="game-canvas" width="800" height="600"></canvas>
                    <div id="game-ui">
                        <div class="score">Score: <span id="score">0</span></div>
                        <div class="level">Level: <span id="level">1</span></div>
                    </div>
                </div>
                <div class="game-instructions" id="game-instructions">
                    <!-- Game instructions will be loaded here -->
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>GameHub</h3>
                    <p>Your ultimate destination for browser-based 2D games.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#home" onclick="showPage('home')">Home</a></li>
                        <li><a href="#games" onclick="showPage('games')">Games</a></li>
                        <li><a href="#rankings" onclick="showPage('rankings')">Rankings</a></li>
                        <li><a href="#news" onclick="showPage('news')">News</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 GameHub. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/supabase.js"></script>
    <script src="js/data.js"></script>
    <script src="js/theme.js"></script>
    <script src="js/home.js"></script>
</body>
</html>
