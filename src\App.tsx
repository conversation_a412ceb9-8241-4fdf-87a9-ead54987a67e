import './index.css';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import AllGames from './pages/AllGames';
import Rankings from './pages/Rankings';
import News from './pages/News';
import GameDetail from './pages/GameDetail';
import GamePlay from './pages/GamePlay';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-900 text-white">
        <Header />
        <main className="min-h-screen">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/games" element={<AllGames />} />
            <Route path="/rankings" element={<Rankings />} />
            <Route path="/news" element={<News />} />
            <Route path="/game/:id" element={<GameDetail />} />
            <Route path="/play/:id" element={<GamePlay />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;
