// News data for the dashboard
export interface NewsArticle {
  id: number;
  title: string;
  excerpt: string;
  content: string;
  date: string;
  category: string;
  image: string;
  featured: boolean;
}

export const news: NewsArticle[] = [
  {
    id: 1,
    title: "New Snake Adventure Update Released!",
    excerpt: "Experience enhanced graphics and new power-ups in the latest Snake Adventure update.",
    content: "We're excited to announce the release of Snake Adventure v2.0! This major update includes stunning new graphics, exciting power-ups, and challenging new levels. Players can now collect special items that grant temporary abilities like speed boost, wall pass, and score multiplier.",
    date: "2024-01-15",
    category: "Game Updates",
    image: "/api/placeholder/400/250",
    featured: true
  },
  {
    id: 2,
    title: "Tetris Master Championship Begins",
    excerpt: "Join the ultimate Tetris competition and compete for amazing prizes!",
    content: "The annual Tetris Master Championship is now live! Players from around the world can compete in daily tournaments to climb the leaderboards. The top 100 players will receive exclusive rewards, and the champion will win a special golden trophy badge.",
    date: "2024-01-12",
    category: "Events",
    image: "/api/placeholder/400/250",
    featured: true
  },
  {
    id: 3,
    title: "Platform Maintenance Scheduled",
    excerpt: "Brief maintenance window planned for this weekend to improve performance.",
    content: "We will be performing scheduled maintenance on Saturday, January 20th from 2:00 AM to 4:00 AM EST. During this time, some games may be temporarily unavailable. This maintenance will improve overall platform performance and stability.",
    date: "2024-01-10",
    category: "Maintenance",
    image: "/api/placeholder/400/250",
    featured: false
  },
  {
    id: 4,
    title: "New Multiplayer Features Coming Soon",
    excerpt: "Get ready for real-time multiplayer gaming with friends!",
    content: "We're working on exciting new multiplayer features that will allow you to play with friends in real-time. Starting with Pong Championship and expanding to other games, you'll soon be able to challenge friends and join public matches.",
    date: "2024-01-08",
    category: "Features",
    image: "/api/placeholder/400/250",
    featured: false
  },
  {
    id: 5,
    title: "Mobile App Beta Testing",
    excerpt: "Sign up for our mobile app beta and play your favorite games on the go!",
    content: "Our mobile app is entering beta testing phase! Experience all your favorite browser games optimized for mobile devices. Beta testers will get early access to new features and the ability to sync progress across devices.",
    date: "2024-01-05",
    category: "Mobile",
    image: "/api/placeholder/400/250",
    featured: false
  },
  {
    id: 6,
    title: "Holiday Event Results",
    excerpt: "Thank you to everyone who participated in our holiday gaming event!",
    content: "Our holiday event was a huge success with over 50,000 participants! Congratulations to all the winners who earned special holiday-themed badges and rewards. Stay tuned for our next seasonal event coming in spring.",
    date: "2024-01-02",
    category: "Events",
    image: "/api/placeholder/400/250",
    featured: false
  }
];

export const getLatestNews = (limit: number = 3): NewsArticle[] => news.slice(0, limit);
export const getFeaturedNews = (): NewsArticle[] => news.filter(article => article.featured);
export const getNewsByCategory = (category: string): NewsArticle[] => 
  category === "All" ? news : news.filter(article => article.category === category);
