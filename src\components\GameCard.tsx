import { Link } from 'react-router-dom';
import { Star, Play, Users } from 'lucide-react';
import { Game } from '../data/games';

interface GameCardProps {
  game: Game;
  size?: 'small' | 'medium' | 'large';
}

const GameCard = ({ game, size = 'medium' }: GameCardProps) => {
  const sizeClasses = {
    small: 'w-full max-w-sm',
    medium: 'w-full max-w-md',
    large: 'w-full max-w-lg'
  };

  const formatPlays = (plays: number) => {
    if (plays >= 1000) {
      return `${(plays / 1000).toFixed(1)}k`;
    }
    return plays.toString();
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'hard': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className={`${sizeClasses[size]} game-card bg-gray-800 rounded-lg overflow-hidden shadow-lg`}>
      {/* Game Image */}
      <div className="relative">
        <img
          src={`https://picsum.photos/400/250?random=${game.id}`}
          alt={game.title}
          className="w-full h-48 object-cover"
        />
        {game.featured && (
          <div className="absolute top-2 left-2 bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-semibold">
            Featured
          </div>
        )}
        <div className={`absolute top-2 right-2 ${getDifficultyColor(game.difficulty)} text-white px-2 py-1 rounded-full text-xs font-semibold`}>
          {game.difficulty}
        </div>

        {/* Play Button Overlay */}
        <Link
          to={`/play/${game.id}`}
          className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-50 flex items-center justify-center transition-all duration-300 group"
        >
          <Play className="h-12 w-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </Link>
      </div>

      {/* Game Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-lg font-semibold text-white truncate flex-1">
            {game.title}
          </h3>
          <div className="flex items-center space-x-1 ml-2">
            <Star className="h-4 w-4 text-yellow-400 fill-current" />
            <span className="text-sm text-gray-300">{game.rating}</span>
          </div>
        </div>

        <p className="text-gray-400 text-sm mb-3 line-clamp-2">
          {game.description}
        </p>

        {/* Tags */}
        <div className="flex flex-wrap gap-1 mb-3">
          {game.tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="bg-gray-700 text-gray-300 px-2 py-1 rounded-full text-xs"
            >
              {tag}
            </span>
          ))}
        </div>

        {/* Stats and Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-400">
            <div className="flex items-center space-x-1">
              <Users className="h-4 w-4" />
              <span>{formatPlays(game.plays)}</span>
            </div>
            <span className="text-blue-400">{game.category}</span>
          </div>

          <div className="flex space-x-2">
            <Link
              to={`/game/${game.id}`}
              className="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors duration-200"
            >
              Details
            </Link>
            <Link
              to={`/play/${game.id}`}
              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors duration-200"
            >
              Play
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameCard;
