// Profile Page Functionality
class ProfilePage {
    constructor() {
        this.userStats = {};
        this.gamePerformance = {};
        this.achievements = [];
        this.activityFeed = [];
        this.init();
    }

    init() {
        this.loadUserData();
        this.setupProfileActions();
        this.setupAnimations();
        this.updateProfileDisplay();
    }

    async loadUserData() {
        try {
            // Load data from Supabase if available, otherwise from localStorage
            if (window.supabaseClient && window.supabaseClient.isAuthenticated()) {
                this.userStats = await window.supabaseClient.getUserData();
            } else {
                this.userStats = this.getLocalUserStats();
            }

            this.gamePerformance = this.getGamePerformance();
            this.achievements = this.getAchievements();
            this.activityFeed = this.getActivityFeed();

            this.updateProfileDisplay();
        } catch (error) {
            console.error('Error loading user data:', error);
            this.userStats = this.getLocalUserStats();
            this.updateProfileDisplay();
        }
    }

    getLocalUserStats() {
        return {
            gamesPlayed: parseInt(localStorage.getItem('games-played') || '0'),
            bestScore: parseInt(localStorage.getItem('best-score') || '0'),
            timePlayed: parseInt(localStorage.getItem('time-played') || '0'), // in minutes
            achievements: parseInt(localStorage.getItem('achievements') || '0'),
            joinDate: localStorage.getItem('join-date') || new Date().toISOString(),
            username: localStorage.getItem('username') || 'Guest Player'
        };
    }

    getGamePerformance() {
        const games = ['snake', 'tetris', 'pong'];
        const performance = {};

        games.forEach(gameId => {
            const scores = JSON.parse(localStorage.getItem(`${gameId}-scores`) || '[]');
            const gamesPlayed = scores.length;
            const bestScore = Math.max(...scores.map(s => s.score || 0), 0);
            const avgScore = gamesPlayed > 0 ? Math.round(scores.reduce((sum, s) => sum + (s.score || 0), 0) / gamesPlayed) : 0;

            performance[gameId] = {
                gamesPlayed,
                bestScore,
                avgScore,
                lastPlayed: scores.length > 0 ? scores[scores.length - 1].date : null
            };
        });

        return performance;
    }

    getAchievements() {
        const achievements = [
            {
                id: 'first_victory',
                name: 'First Victory',
                description: 'Win your first game',
                icon: '🏆',
                progress: Math.min(this.userStats.gamesPlayed, 1),
                target: 1,
                unlocked: this.userStats.gamesPlayed >= 1
            },
            {
                id: 'hot_streak',
                name: 'Hot Streak',
                description: 'Win 5 games in a row',
                icon: '🔥',
                progress: 0, // Would need to track win streaks
                target: 5,
                unlocked: false
            },
            {
                id: 'high_scorer',
                name: 'High Scorer',
                description: 'Reach 10,000 points in any game',
                icon: '⭐',
                progress: Math.min(this.userStats.bestScore, 10000),
                target: 10000,
                unlocked: this.userStats.bestScore >= 10000
            },
            {
                id: 'perfectionist',
                name: 'Perfectionist',
                description: 'Complete a perfect game',
                icon: '🎯',
                progress: 0, // Would need to track perfect games
                target: 1,
                unlocked: false
            },
            {
                id: 'dedicated_player',
                name: 'Dedicated Player',
                description: 'Play for 10 hours total',
                icon: '⏰',
                progress: Math.min(this.userStats.timePlayed, 600), // 10 hours = 600 minutes
                target: 600,
                unlocked: this.userStats.timePlayed >= 600
            },
            {
                id: 'game_master',
                name: 'Game Master',
                description: 'Play all available games',
                icon: '🌟',
                progress: Object.keys(this.gamePerformance).filter(game => this.gamePerformance[game].gamesPlayed > 0).length,
                target: 8,
                unlocked: false
            }
        ];

        return achievements;
    }

    getActivityFeed() {
        const recentScores = JSON.parse(localStorage.getItem('recent-scores') || '[]');
        const recentAchievements = JSON.parse(localStorage.getItem('recent-achievements') || '[]');
        
        const activities = [
            ...recentScores.map(score => ({
                type: 'score',
                game: score.game,
                score: score.score,
                timestamp: score.timestamp,
                icon: '🎮'
            })),
            ...recentAchievements.map(achievement => ({
                type: 'achievement',
                name: achievement.name,
                timestamp: achievement.timestamp,
                icon: '🏆'
            }))
        ];

        return activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)).slice(0, 10);
    }

    updateProfileDisplay() {
        this.updateProfileHeader();
        this.updateStatsCards();
        this.updateGamePerformance();
        this.updateAchievements();
        this.updateActivityFeed();
    }

    updateProfileHeader() {
        const nameElement = document.querySelector('.profile-name');
        const statusElement = document.querySelector('.profile-status');
        
        if (nameElement) {
            nameElement.textContent = this.userStats.username || 'Guest Player';
        }
        
        if (statusElement) {
            const joinDate = new Date(this.userStats.joinDate || Date.now());
            const daysSince = Math.floor((Date.now() - joinDate.getTime()) / (1000 * 60 * 60 * 24));
            statusElement.textContent = daysSince === 0 ? 'Playing since today' : `Playing since ${daysSince} days ago`;
        }
    }

    updateStatsCards() {
        const elements = {
            'profile-games-played': this.userStats.gamesPlayed || 0,
            'profile-best-score': this.formatNumber(this.userStats.bestScore || 0),
            'profile-time-played': this.formatTime(this.userStats.timePlayed || 0),
            'profile-achievements': this.achievements.filter(a => a.unlocked).length
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                this.animateCounter(element, value);
            }
        });
    }

    updateGamePerformance() {
        const gameCards = document.querySelectorAll('.performance-card');
        const games = ['snake', 'tetris', 'pong'];
        
        gameCards.forEach((card, index) => {
            const gameId = games[index];
            if (!gameId || !this.gamePerformance[gameId]) return;
            
            const performance = this.gamePerformance[gameId];
            const perfStats = card.querySelectorAll('.perf-value');
            
            if (perfStats.length >= 3) {
                perfStats[0].textContent = this.formatNumber(performance.bestScore);
                perfStats[1].textContent = performance.gamesPlayed;
                perfStats[2].textContent = this.formatNumber(performance.avgScore);
            }
        });
    }

    updateAchievements() {
        const achievementsGrid = document.querySelector('.achievements-grid');
        if (!achievementsGrid) return;

        achievementsGrid.innerHTML = '';
        
        this.achievements.forEach(achievement => {
            const achievementCard = this.createAchievementCard(achievement);
            achievementsGrid.appendChild(achievementCard);
        });
    }

    createAchievementCard(achievement) {
        const card = document.createElement('div');
        card.className = `achievement-card ${achievement.unlocked ? 'unlocked' : 'locked'}`;
        
        const progressPercent = Math.round((achievement.progress / achievement.target) * 100);
        
        card.innerHTML = `
            <div class="achievement-icon">${achievement.icon}</div>
            <div class="achievement-info">
                <h3>${achievement.name}</h3>
                <p>${achievement.description}</p>
                <div class="achievement-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progressPercent}%"></div>
                    </div>
                    <span class="progress-text">${achievement.progress}/${achievement.target}</span>
                </div>
            </div>
        `;
        
        return card;
    }

    updateActivityFeed() {
        const activityFeed = document.getElementById('activity-feed');
        if (!activityFeed) return;

        if (this.activityFeed.length === 0) {
            activityFeed.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📊</div>
                    <h3>No activity yet</h3>
                    <p>Start playing games to see your activity here!</p>
                    <a href="games.html" class="btn btn-primary">Browse Games</a>
                </div>
            `;
            return;
        }

        const feedHTML = this.activityFeed.map(activity => {
            if (activity.type === 'score') {
                return `
                    <div class="activity-item">
                        <div class="activity-icon">${activity.icon}</div>
                        <div class="activity-content">
                            <div class="activity-title">New High Score!</div>
                            <div class="activity-description">Scored ${this.formatNumber(activity.score)} in ${activity.game}</div>
                            <div class="activity-time">${this.formatRelativeTime(activity.timestamp)}</div>
                        </div>
                    </div>
                `;
            } else if (activity.type === 'achievement') {
                return `
                    <div class="activity-item">
                        <div class="activity-icon">${activity.icon}</div>
                        <div class="activity-content">
                            <div class="activity-title">Achievement Unlocked!</div>
                            <div class="activity-description">${activity.name}</div>
                            <div class="activity-time">${this.formatRelativeTime(activity.timestamp)}</div>
                        </div>
                    </div>
                `;
            }
        }).join('');

        activityFeed.innerHTML = feedHTML;
    }

    setupProfileActions() {
        // Create Account button
        const createAccountBtns = document.querySelectorAll('.btn:contains("Create Account")');
        createAccountBtns.forEach(btn => {
            if (btn.textContent.includes('Create Account')) {
                btn.addEventListener('click', () => this.showAuthModal('signup'));
            }
        });

        // Sign In button
        const signInBtns = document.querySelectorAll('.btn:contains("Sign In")');
        signInBtns.forEach(btn => {
            if (btn.textContent.includes('Sign In')) {
                btn.addEventListener('click', () => this.showAuthModal('signin'));
            }
        });

        // Avatar edit button
        const avatarEditBtn = document.querySelector('.avatar-edit');
        if (avatarEditBtn) {
            avatarEditBtn.addEventListener('click', () => this.showAvatarSelector());
        }
    }

    setupAnimations() {
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe stat cards
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in');
            observer.observe(card);
        });

        // Observe performance cards
        const performanceCards = document.querySelectorAll('.performance-card');
        performanceCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.15}s`;
            card.classList.add('slide-up');
            observer.observe(card);
        });

        // Observe achievement cards
        const achievementCards = document.querySelectorAll('.achievement-card');
        achievementCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('scale-in');
            observer.observe(card);
        });
    }

    // Utility Methods
    animateCounter(element, targetValue) {
        const isNumeric = !isNaN(targetValue);
        if (!isNumeric) {
            element.textContent = targetValue;
            return;
        }

        const startValue = 0;
        const duration = 1000;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
            element.textContent = this.formatNumber(currentValue);

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'k';
        }
        return num.toString();
    }

    formatTime(minutes) {
        if (minutes >= 60) {
            const hours = Math.floor(minutes / 60);
            const remainingMinutes = minutes % 60;
            return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
        }
        return `${minutes}m`;
    }

    formatRelativeTime(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffMs = now - time;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins} minutes ago`;
        if (diffHours < 24) return `${diffHours} hours ago`;
        if (diffDays < 7) return `${diffDays} days ago`;
        return time.toLocaleDateString();
    }

    showAuthModal(type) {
        // This would show a modal for authentication
        // For now, just redirect to a placeholder
        alert(`${type === 'signup' ? 'Sign Up' : 'Sign In'} functionality would be implemented here with Supabase authentication.`);
    }

    showAvatarSelector() {
        // This would show an avatar selection modal
        alert('Avatar selection functionality would be implemented here.');
    }

    // Save user data
    async saveUserData() {
        if (window.supabaseClient && window.supabaseClient.isAuthenticated()) {
            await window.supabaseClient.saveUserData(this.userStats);
        } else {
            // Save to localStorage
            Object.entries(this.userStats).forEach(([key, value]) => {
                localStorage.setItem(key.replace(/([A-Z])/g, '-$1').toLowerCase(), value.toString());
            });
        }
    }
}

// Game play function
function playGame(gameId) {
    // Add to recently played
    let recentlyPlayed = JSON.parse(localStorage.getItem('recently-played') || '[]');
    recentlyPlayed = recentlyPlayed.filter(id => id !== gameId);
    recentlyPlayed.unshift(gameId);
    recentlyPlayed = recentlyPlayed.slice(0, 10);
    localStorage.setItem('recently-played', JSON.stringify(recentlyPlayed));

    // Redirect to games page with game ID
    window.location.href = `games.html?play=${gameId}`;
}

// Initialize profile page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.body.contains(document.querySelector('.profile-header'))) {
        window.profilePage = new ProfilePage();
    }
});
