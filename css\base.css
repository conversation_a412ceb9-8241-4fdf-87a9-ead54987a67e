/* CSS Custom Properties for Theme Support */
:root {
    /* Dark Theme (Default) */
    --primary-bg: #0f1419;
    --secondary-bg: #1a2332;
    --tertiary-bg: #2a3441;
    --text-primary: #ffffff;
    --text-secondary: #a0a9b8;
    --text-muted: #6b7280;
    --accent-primary: #00d4ff;
    --accent-secondary: #0099cc;
    --accent-tertiary: #007acc;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --border-color: #2a3441;
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.2);
    --shadow-heavy: rgba(0, 0, 0, 0.3);
    --gradient-primary: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-bg), var(--tertiary-bg));
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    --border-radius-sm: 6px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
}

/* Light Theme */
[data-theme="light"] {
    --primary-bg: #ffffff;
    --secondary-bg: #f8fafc;
    --tertiary-bg: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #94a3b8;
    --accent-primary: #0ea5e9;
    --accent-secondary: #0284c7;
    --accent-tertiary: #0369a1;
    --border-color: #e2e8f0;
    --shadow-light: rgba(0, 0, 0, 0.05);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --shadow-heavy: rgba(0, 0, 0, 0.15);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    transition: background-color var(--transition-medium), color var(--transition-medium);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.5em;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1em;
}

a {
    color: var(--accent-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--accent-secondary);
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.main-content {
    min-height: calc(100vh - 140px);
    padding-top: 0;
}

.section {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 50px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
    gap: 20px;
}

.view-all-link {
    color: var(--accent-primary);
    font-weight: 500;
    transition: color var(--transition-fast);
}

.view-all-link:hover {
    color: var(--accent-secondary);
}

.bg-dark {
    background: var(--secondary-bg);
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.hidden {
    display: none !important;
}

/* Focus Styles */
*:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loaded {
    animation: fadeIn 0.5s ease;
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease forwards;
}

.slide-up {
    opacity: 0;
    transform: translateY(30px);
    animation: slideUp 0.6s ease forwards;
}

.scale-in {
    opacity: 0;
    transform: scale(0.9);
    animation: scaleIn 0.6s ease forwards;
}

.animate-in {
    animation-play-state: running;
}

/* Keyframes */
@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes floatIcon {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-30px) rotate(180deg);
        opacity: 0.6;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Responsive Breakpoints */
@media (max-width: 1200px) {
    .container {
        max-width: 100%;
        padding: 0 30px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 20px;
    }
    
    .section {
        padding: 60px 0;
    }
    
    .section-title {
        font-size: 2rem;
        margin-bottom: 30px;
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.25rem; }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .section {
        padding: 40px 0;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
}
