// Pong Game Implementation
class PongGame {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        
        this.paddleWidth = 10;
        this.paddleHeight = 80;
        this.ballSize = 10;
        
        this.player = {
            x: 10,
            y: canvas.height / 2 - this.paddleHeight / 2,
            width: this.paddleWidth,
            height: this.paddleHeight,
            score: 0,
            speed: 5
        };
        
        this.ai = {
            x: canvas.width - 20,
            y: canvas.height / 2 - this.paddleHeight / 2,
            width: this.paddleWidth,
            height: this.paddleHeight,
            score: 0,
            speed: 4
        };
        
        this.ball = {
            x: canvas.width / 2,
            y: canvas.height / 2,
            width: this.ballSize,
            height: this.ballSize,
            speedX: 5,
            speedY: 3
        };
        
        this.gameRunning = false;
        this.gamePaused = false;
        this.keys = {};
        this.maxScore = 10;
        
        this.init();
    }
    
    init() {
        this.setupControls();
        this.gameRunning = true;
        this.gameLoop();
        this.updateScore();
    }
    
    setupControls() {
        document.addEventListener('keydown', (e) => {
            this.keys[e.key] = true;
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.key] = false;
        });
    }
    
    update() {
        if (!this.gameRunning || this.gamePaused) return;
        
        // Player movement
        if (this.keys['ArrowUp'] || this.keys['w'] || this.keys['W']) {
            this.player.y -= this.player.speed;
        }
        if (this.keys['ArrowDown'] || this.keys['s'] || this.keys['S']) {
            this.player.y += this.player.speed;
        }
        
        // Keep player paddle in bounds
        this.player.y = Math.max(0, Math.min(this.canvas.height - this.player.height, this.player.y));
        
        // AI movement
        const ballCenterY = this.ball.y + this.ball.height / 2;
        const aiCenterY = this.ai.y + this.ai.height / 2;
        
        if (ballCenterY < aiCenterY - 10) {
            this.ai.y -= this.ai.speed;
        } else if (ballCenterY > aiCenterY + 10) {
            this.ai.y += this.ai.speed;
        }
        
        // Keep AI paddle in bounds
        this.ai.y = Math.max(0, Math.min(this.canvas.height - this.ai.height, this.ai.y));
        
        // Ball movement
        this.ball.x += this.ball.speedX;
        this.ball.y += this.ball.speedY;
        
        // Ball collision with top and bottom walls
        if (this.ball.y <= 0 || this.ball.y >= this.canvas.height - this.ball.height) {
            this.ball.speedY = -this.ball.speedY;
        }
        
        // Ball collision with player paddle
        if (this.ball.x <= this.player.x + this.player.width &&
            this.ball.x + this.ball.width >= this.player.x &&
            this.ball.y <= this.player.y + this.player.height &&
            this.ball.y + this.ball.height >= this.player.y) {
            
            this.ball.speedX = -this.ball.speedX;
            this.ball.x = this.player.x + this.player.width;
            
            // Add some angle based on where the ball hits the paddle
            const hitPos = (this.ball.y + this.ball.height / 2) - (this.player.y + this.player.height / 2);
            this.ball.speedY = hitPos * 0.1;
        }
        
        // Ball collision with AI paddle
        if (this.ball.x + this.ball.width >= this.ai.x &&
            this.ball.x <= this.ai.x + this.ai.width &&
            this.ball.y <= this.ai.y + this.ai.height &&
            this.ball.y + this.ball.height >= this.ai.y) {
            
            this.ball.speedX = -this.ball.speedX;
            this.ball.x = this.ai.x - this.ball.width;
            
            // Add some angle based on where the ball hits the paddle
            const hitPos = (this.ball.y + this.ball.height / 2) - (this.ai.y + this.ai.height / 2);
            this.ball.speedY = hitPos * 0.1;
        }
        
        // Ball goes off screen (scoring)
        if (this.ball.x < 0) {
            this.ai.score++;
            this.resetBall();
            this.updateScore();
        } else if (this.ball.x > this.canvas.width) {
            this.player.score++;
            this.resetBall();
            this.updateScore();
        }
        
        // Check for game end
        if (this.player.score >= this.maxScore || this.ai.score >= this.maxScore) {
            this.gameOver();
        }
    }
    
    resetBall() {
        this.ball.x = this.canvas.width / 2;
        this.ball.y = this.canvas.height / 2;
        this.ball.speedX = -this.ball.speedX;
        this.ball.speedY = (Math.random() - 0.5) * 6;
    }
    
    draw() {
        // Clear canvas
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw center line
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([10, 10]);
        this.ctx.beginPath();
        this.ctx.moveTo(this.canvas.width / 2, 0);
        this.ctx.lineTo(this.canvas.width / 2, this.canvas.height);
        this.ctx.stroke();
        this.ctx.setLineDash([]);
        
        // Draw player paddle
        this.ctx.fillStyle = '#00d4ff';
        this.ctx.fillRect(this.player.x, this.player.y, this.player.width, this.player.height);
        
        // Draw AI paddle
        this.ctx.fillStyle = '#ff4444';
        this.ctx.fillRect(this.ai.x, this.ai.y, this.ai.width, this.ai.height);
        
        // Draw ball
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fillRect(this.ball.x, this.ball.y, this.ball.width, this.ball.height);
        
        // Draw scores
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '48px Inter';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(this.player.score, this.canvas.width / 4, 60);
        this.ctx.fillText(this.ai.score, (this.canvas.width * 3) / 4, 60);
        
        // Draw pause overlay
        if (this.gamePaused) {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            
            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = '48px Inter';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('PAUSED', this.canvas.width / 2, this.canvas.height / 2);
            this.ctx.font = '24px Inter';
            this.ctx.fillText('Press pause button to resume', this.canvas.width / 2, this.canvas.height / 2 + 40);
        }
    }
    
    gameLoop() {
        if (!this.gameRunning) return;
        
        this.update();
        this.draw();
        
        requestAnimationFrame(() => this.gameLoop());
    }
    
    updateScore() {
        const scoreElement = document.getElementById('score');
        if (scoreElement) {
            scoreElement.textContent = `${this.player.score} - ${this.ai.score}`;
        }
    }
    
    gameOver() {
        this.gameRunning = false;
        
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        const winner = this.player.score >= this.maxScore ? 'PLAYER WINS!' : 'AI WINS!';
        const winnerColor = this.player.score >= this.maxScore ? '#00d4ff' : '#ff4444';
        
        this.ctx.fillStyle = winnerColor;
        this.ctx.font = '48px Inter';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(winner, this.canvas.width / 2, this.canvas.height / 2 - 40);
        
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '24px Inter';
        this.ctx.fillText(`Final Score: ${this.player.score} - ${this.ai.score}`, this.canvas.width / 2, this.canvas.height / 2 + 10);
        this.ctx.fillText('Click restart to play again', this.canvas.width / 2, this.canvas.height / 2 + 40);
    }
    
    togglePause() {
        this.gamePaused = !this.gamePaused;
        if (!this.gamePaused && this.gameRunning) {
            this.gameLoop();
        }
    }
    
    restart() {
        this.player.score = 0;
        this.ai.score = 0;
        this.player.y = this.canvas.height / 2 - this.paddleHeight / 2;
        this.ai.y = this.canvas.height / 2 - this.paddleHeight / 2;
        this.ball.x = this.canvas.width / 2;
        this.ball.y = this.canvas.height / 2;
        this.ball.speedX = 5;
        this.ball.speedY = 3;
        this.gameRunning = true;
        this.gamePaused = false;
        this.updateScore();
        this.gameLoop();
    }
    
    stop() {
        this.gameRunning = false;
        this.gamePaused = false;
    }
}
