// Game and News Data
const gamesData = [
    {
        id: 1,
        title: "Snake Adventure",
        description: "Classic snake game with modern graphics and power-ups. Eat food to grow longer while avoiding walls and your own tail!",
        category: "arcade",
        rating: 4.5,
        plays: 15420,
        featured: true,
        icon: "🐍",
        gameType: "snake"
    },
    {
        id: 2,
        title: "Tetris Master",
        description: "The ultimate block puzzle experience. Arrange falling blocks to create complete lines and clear them!",
        category: "puzzle",
        rating: 4.8,
        plays: 23150,
        featured: true,
        icon: "🧩",
        gameType: "tetris"
    },
    {
        id: 3,
        title: "Pong Championship",
        description: "The classic paddle game that started it all. Control your paddle and defeat the AI opponent!",
        category: "action",
        rating: 4.0,
        plays: 9320,
        featured: true,
        icon: "🏓",
        gameType: "pong"
    },
    {
        id: 4,
        title: "Space Invaders",
        description: "Defend Earth from alien invasion in this retro shooter. Move and shoot to destroy all enemies!",
        category: "action",
        rating: 4.3,
        plays: 18750,
        featured: false,
        icon: "👾",
        gameType: "space-invaders"
    },
    {
        id: 5,
        title: "Breakout Deluxe",
        description: "Break bricks with your ball and paddle. Clear all bricks to advance to the next level!",
        category: "arcade",
        rating: 4.6,
        plays: 12890,
        featured: false,
        icon: "🧱",
        gameType: "breakout"
    },
    {
        id: 6,
        title: "Pac-Man Adventure",
        description: "Navigate mazes and collect dots while avoiding colorful ghosts in this classic arcade game!",
        category: "arcade",
        rating: 4.7,
        plays: 21340,
        featured: false,
        icon: "🟡",
        gameType: "pacman"
    },
    {
        id: 7,
        title: "Frogger Crossing",
        description: "Help the frog cross busy roads and rivers safely. Avoid cars and use logs to reach the other side!",
        category: "arcade",
        rating: 4.2,
        plays: 8760,
        featured: false,
        icon: "🐸",
        gameType: "frogger"
    },
    {
        id: 8,
        title: "Asteroids Blast",
        description: "Pilot your ship through dangerous asteroid fields. Shoot asteroids and avoid collisions!",
        category: "action",
        rating: 4.4,
        plays: 14520,
        featured: false,
        icon: "🚀",
        gameType: "asteroids"
    }
];

const newsData = [
    {
        id: 1,
        title: "New Snake Adventure Update Released!",
        excerpt: "Experience enhanced graphics and new power-ups in the latest Snake Adventure update.",
        category: "updates",
        date: "2024-01-15",
        icon: "🆕"
    },
    {
        id: 2,
        title: "Tetris Master Championship Begins",
        excerpt: "Join the ultimate Tetris competition and compete for amazing prizes!",
        category: "events",
        date: "2024-01-12",
        icon: "🏆"
    },
    {
        id: 3,
        title: "Platform Maintenance Scheduled",
        excerpt: "Brief maintenance window planned for this weekend to improve performance.",
        category: "updates",
        date: "2024-01-10",
        icon: "🔧"
    },
    {
        id: 4,
        title: "New Multiplayer Features Coming Soon",
        excerpt: "Get ready for real-time multiplayer gaming with friends!",
        category: "features",
        date: "2024-01-08",
        icon: "👥"
    },
    {
        id: 5,
        title: "Mobile App Beta Testing",
        excerpt: "Sign up for our mobile app beta and play your favorite games on the go!",
        category: "features",
        date: "2024-01-05",
        icon: "📱"
    },
    {
        id: 6,
        title: "Holiday Event Results",
        excerpt: "Thank you to everyone who participated in our holiday gaming event!",
        category: "events",
        date: "2024-01-02",
        icon: "🎉"
    }
];

// Utility functions
function getFeaturedGames() {
    return gamesData.filter(game => game.featured);
}

function getGamesByCategory(category) {
    if (category === 'all') return gamesData;
    return gamesData.filter(game => game.category === category);
}

function getTopRatedGames() {
    return [...gamesData].sort((a, b) => b.rating - a.rating);
}

function getMostPlayedGames() {
    return [...gamesData].sort((a, b) => b.plays - a.plays);
}

function getGameById(id) {
    return gamesData.find(game => game.id === parseInt(id));
}

function getNewsByCategory(category) {
    if (category === 'all') return newsData;
    return newsData.filter(article => article.category === category);
}

function getLatestNews(limit = 3) {
    return newsData.slice(0, limit);
}

function searchGames(query) {
    const lowercaseQuery = query.toLowerCase();
    return gamesData.filter(game => 
        game.title.toLowerCase().includes(lowercaseQuery) ||
        game.description.toLowerCase().includes(lowercaseQuery) ||
        game.category.toLowerCase().includes(lowercaseQuery)
    );
}

function sortGames(games, sortBy) {
    const gamesCopy = [...games];
    switch (sortBy) {
        case 'name':
            return gamesCopy.sort((a, b) => a.title.localeCompare(b.title));
        case 'rating':
            return gamesCopy.sort((a, b) => b.rating - a.rating);
        case 'plays':
            return gamesCopy.sort((a, b) => b.plays - a.plays);
        default:
            return gamesCopy;
    }
}

function formatNumber(num) {
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}
