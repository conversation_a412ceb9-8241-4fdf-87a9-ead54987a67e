/* Page Headers */
.page-header {
    background: var(--gradient-secondary);
    padding: 100px 0 60px;
    text-align: center;
    margin-top: 70px;
    position: relative;
    overflow: hidden;
}

.page-header-content {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 20px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.title-icon {
    font-size: 3.5rem;
    filter: drop-shadow(0 0 10px var(--accent-primary));
}

.page-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 30px;
    line-height: 1.6;
}

.page-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* Hero Section */
.hero {
    background: var(--gradient-secondary);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin-top: 70px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 30%;
    animation-delay: 1s;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    line-height: 1.1;
}

.title-line {
    display: block;
    color: var(--text-secondary);
    font-size: 0.7em;
}

.title-highlight {
    display: block;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 60px;
    flex-wrap: wrap;
}

/* Floating Icons Animation */
.floating-icons {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.float-icon {
    position: absolute;
    font-size: 2rem;
    opacity: 0.3;
    animation: floatIcon 8s ease-in-out infinite;
}

.float-icon:nth-child(1) {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
}

.float-icon:nth-child(2) {
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.float-icon:nth-child(3) {
    top: 80%;
    left: 25%;
    animation-delay: 4s;
}

.float-icon:nth-child(4) {
    top: 30%;
    right: 30%;
    animation-delay: 6s;
}

/* Hero Stats */
.hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 40px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: transform var(--transition-fast);
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    color: var(--accent-primary);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Quick Stats Dashboard */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-3px);
    border-color: var(--accent-primary);
    box-shadow: 0 10px 25px var(--shadow-medium);
}

.stat-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    flex-shrink: 0;
}

.stat-info h3 {
    color: var(--text-primary);
    font-size: 1.1rem;
    margin-bottom: 5px;
    font-weight: 600;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--accent-primary);
}

/* Help Page Specific */
.quick-help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.help-card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    text-align: center;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.help-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.help-card:hover::before {
    left: 100%;
}

.help-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-primary);
    box-shadow: 0 10px 25px var(--shadow-medium);
}

.help-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.help-card h3 {
    color: var(--text-primary);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.help-card p {
    color: var(--text-secondary);
    margin-bottom: 20px;
    line-height: 1.6;
}

.help-link {
    color: var(--accent-primary);
    font-weight: 500;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.help-link:hover {
    color: var(--accent-secondary);
}

/* Tutorials Grid */
.tutorials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.tutorial-card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    transition: all var(--transition-fast);
}

.tutorial-card:hover {
    transform: translateY(-3px);
    border-color: var(--accent-primary);
    box-shadow: 0 10px 25px var(--shadow-medium);
}

.tutorial-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
}

.tutorial-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
}

.tutorial-header h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    margin: 0;
}

.tutorial-content h4 {
    color: var(--accent-primary);
    margin-bottom: 15px;
    margin-top: 20px;
    font-size: 1.1rem;
}

.tutorial-content ol,
.tutorial-content ul {
    margin-bottom: 20px;
    padding-left: 20px;
}

.tutorial-content li {
    color: var(--text-secondary);
    margin-bottom: 8px;
    line-height: 1.5;
}

/* Troubleshooting Sections */
.troubleshooting-sections {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.trouble-section {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 30px;
}

.trouble-section h3 {
    color: var(--text-primary);
    margin-bottom: 25px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.trouble-solutions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.solution {
    background: var(--tertiary-bg);
    border-radius: var(--border-radius-md);
    padding: 20px;
}

.solution h4 {
    color: var(--accent-primary);
    margin-bottom: 10px;
    font-size: 1rem;
}

.solution p {
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

/* FAQ Components */
.faq-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.faq-item {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all var(--transition-fast);
}

.faq-item.active {
    border-color: var(--accent-primary);
}

.faq-question {
    width: 100%;
    background: none;
    border: none;
    padding: 20px 25px;
    text-align: left;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all var(--transition-fast);
}

.faq-question:hover {
    background: var(--tertiary-bg);
}

.faq-icon {
    font-size: 1.2rem;
    color: var(--accent-primary);
    transition: transform var(--transition-fast);
}

.faq-item.active .faq-icon {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-medium);
}

.faq-answer p {
    padding: 0 25px 25px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* Support CTA */
.support-cta {
    text-align: center;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-xl);
    padding: 50px 30px;
}

.support-cta h2 {
    color: var(--text-primary);
    margin-bottom: 15px;
    font-size: 2rem;
}

.support-cta p {
    color: var(--text-secondary);
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.support-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}
