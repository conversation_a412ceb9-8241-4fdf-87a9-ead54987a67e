/* Page Headers */
.page-header {
    background: var(--gradient-secondary);
    padding: 100px 0 60px;
    text-align: center;
    margin-top: 70px;
    position: relative;
    overflow: hidden;
}

.page-header-content {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 20px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.title-icon {
    font-size: 3.5rem;
    filter: drop-shadow(0 0 10px var(--accent-primary));
}

.page-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 30px;
    line-height: 1.6;
}

.page-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* Hero Section */
.hero {
    background: var(--gradient-secondary);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin-top: 70px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 30%;
    animation-delay: 1s;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    line-height: 1.1;
}

.title-line {
    display: block;
    color: var(--text-secondary);
    font-size: 0.7em;
}

.title-highlight {
    display: block;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 60px;
    flex-wrap: wrap;
}

/* Floating Icons Animation */
.floating-icons {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.float-icon {
    position: absolute;
    font-size: 2rem;
    opacity: 0.3;
    animation: floatIcon 8s ease-in-out infinite;
}

.float-icon:nth-child(1) {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
}

.float-icon:nth-child(2) {
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.float-icon:nth-child(3) {
    top: 80%;
    left: 25%;
    animation-delay: 4s;
}

.float-icon:nth-child(4) {
    top: 30%;
    right: 30%;
    animation-delay: 6s;
}

/* Hero Stats */
.hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 40px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: transform var(--transition-fast);
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    color: var(--accent-primary);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Quick Stats Dashboard */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-3px);
    border-color: var(--accent-primary);
    box-shadow: 0 10px 25px var(--shadow-medium);
}

.stat-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    flex-shrink: 0;
}

.stat-info h3 {
    color: var(--text-primary);
    font-size: 1.1rem;
    margin-bottom: 5px;
    font-weight: 600;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--accent-primary);
}

/* Help Page Specific */
.quick-help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.help-card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    text-align: center;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.help-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.help-card:hover::before {
    left: 100%;
}

.help-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-primary);
    box-shadow: 0 10px 25px var(--shadow-medium);
}

.help-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.help-card h3 {
    color: var(--text-primary);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.help-card p {
    color: var(--text-secondary);
    margin-bottom: 20px;
    line-height: 1.6;
}

.help-link {
    color: var(--accent-primary);
    font-weight: 500;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.help-link:hover {
    color: var(--accent-secondary);
}

/* Tutorials Grid */
.tutorials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.tutorial-card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    transition: all var(--transition-fast);
}

.tutorial-card:hover {
    transform: translateY(-3px);
    border-color: var(--accent-primary);
    box-shadow: 0 10px 25px var(--shadow-medium);
}

.tutorial-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
}

.tutorial-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
}

.tutorial-header h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    margin: 0;
}

.tutorial-content h4 {
    color: var(--accent-primary);
    margin-bottom: 15px;
    margin-top: 20px;
    font-size: 1.1rem;
}

.tutorial-content ol,
.tutorial-content ul {
    margin-bottom: 20px;
    padding-left: 20px;
}

.tutorial-content li {
    color: var(--text-secondary);
    margin-bottom: 8px;
    line-height: 1.5;
}

/* Troubleshooting Sections */
.troubleshooting-sections {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.trouble-section {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 30px;
}

.trouble-section h3 {
    color: var(--text-primary);
    margin-bottom: 25px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.trouble-solutions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.solution {
    background: var(--tertiary-bg);
    border-radius: var(--border-radius-md);
    padding: 20px;
}

.solution h4 {
    color: var(--accent-primary);
    margin-bottom: 10px;
    font-size: 1rem;
}

.solution p {
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

/* FAQ Components */
.faq-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.faq-item {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all var(--transition-fast);
}

.faq-item.active {
    border-color: var(--accent-primary);
}

.faq-question {
    width: 100%;
    background: none;
    border: none;
    padding: 20px 25px;
    text-align: left;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all var(--transition-fast);
}

.faq-question:hover {
    background: var(--tertiary-bg);
}

.faq-icon {
    font-size: 1.2rem;
    color: var(--accent-primary);
    transition: transform var(--transition-fast);
}

.faq-item.active .faq-icon {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-medium);
}

.faq-answer p {
    padding: 0 25px 25px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* Support CTA */
.support-cta {
    text-align: center;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-xl);
    padding: 50px 30px;
}

.support-cta h2 {
    color: var(--text-primary);
    margin-bottom: 15px;
    font-size: 2rem;
}

.support-cta p {
    color: var(--text-secondary);
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.support-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Profile Page Styles */
.profile-header {
    background: var(--gradient-secondary);
    padding: 100px 0 60px;
    margin-top: 70px;
    position: relative;
    overflow: hidden;
}

.profile-info {
    display: flex;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
}

.profile-avatar {
    position: relative;
    width: 120px;
    height: 120px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    margin: 0 auto 20px;
}

.avatar-edit {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 35px;
    height: 35px;
    background: var(--secondary-bg);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1rem;
    transition: all var(--transition-fast);
}

.avatar-edit:hover {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    transform: scale(1.1);
}

.profile-details {
    flex: 1;
    min-width: 250px;
}

.profile-name {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 10px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.profile-status {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.profile-badges {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.badge {
    padding: 6px 12px;
    border-radius: var(--border-radius-md);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-new {
    background: var(--warning);
    color: var(--primary-bg);
}

.badge-active {
    background: var(--success);
    color: var(--primary-bg);
}

.profile-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.profile-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-change {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 5px;
}

/* Game Performance */
.game-performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.performance-card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    transition: all var(--transition-fast);
}

.performance-card:hover {
    transform: translateY(-3px);
    border-color: var(--accent-primary);
    box-shadow: 0 10px 25px var(--shadow-medium);
}

.game-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.game-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
}

.game-info h3 {
    color: var(--text-primary);
    margin-bottom: 5px;
    font-size: 1.3rem;
}

.game-info p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

.performance-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.perf-stat {
    text-align: center;
    padding: 15px;
    background: var(--tertiary-bg);
    border-radius: var(--border-radius-md);
}

.perf-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.perf-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-primary);
}

.performance-actions {
    text-align: center;
}

/* Achievements */
.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.achievement-card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.achievement-card.locked {
    opacity: 0.6;
}

.achievement-card.unlocked {
    border-color: var(--success);
    background: linear-gradient(135deg, var(--secondary-bg), rgba(16, 185, 129, 0.1));
}

.achievement-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--shadow-medium);
}

.achievement-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    flex-shrink: 0;
}

.achievement-info {
    flex: 1;
}

.achievement-info h3 {
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 1.2rem;
}

.achievement-info p {
    color: var(--text-secondary);
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.achievement-progress {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: var(--tertiary-bg);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    transition: width var(--transition-medium);
}

.progress-text {
    font-size: 0.8rem;
    color: var(--text-muted);
    white-space: nowrap;
}

/* Account Benefits */
.account-benefits {
    text-align: center;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-xl);
    padding: 50px 30px;
}

.account-benefits h2 {
    color: var(--text-primary);
    margin-bottom: 15px;
    font-size: 2.2rem;
}

.account-benefits > p {
    color: var(--text-secondary);
    margin-bottom: 40px;
    font-size: 1.1rem;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.benefit-item {
    text-align: center;
    padding: 25px;
    background: var(--tertiary-bg);
    border-radius: var(--border-radius-lg);
    transition: transform var(--transition-fast);
}

.benefit-item:hover {
    transform: translateY(-5px);
}

.benefit-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
}

.benefit-item h3 {
    color: var(--text-primary);
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.benefit-item p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

.benefits-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* News Page Styles */
.featured-article {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-xl);
    padding: 40px;
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
}

.featured-article::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.featured-article:hover::before {
    left: 100%;
}

.featured-badge {
    display: inline-block;
    background: var(--gradient-primary);
    color: var(--primary-bg);
    padding: 6px 12px;
    border-radius: var(--border-radius-md);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 15px;
}

.featured-category {
    color: var(--accent-primary);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 15px;
}

.featured-article h2 {
    color: var(--text-primary);
    font-size: 2.2rem;
    font-weight: 800;
    margin-bottom: 20px;
    line-height: 1.2;
}

.featured-article p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 25px;
}

.featured-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
    flex-wrap: wrap;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.featured-image {
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-placeholder {
    width: 100%;
    height: 200px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    opacity: 0.8;
}

/* News Categories */
.news-categories {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 40px;
}

.category-btn {
    padding: 12px 24px;
    background: transparent;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.category-btn:hover,
.category-btn.active {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    color: var(--primary-bg);
    transform: translateY(-2px);
}

/* News Grid */
.news-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
    gap: 20px;
}

.news-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.news-sort {
    padding: 10px 15px;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    color: var(--text-primary);
    font-size: 0.9rem;
    cursor: pointer;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.news-card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all var(--transition-fast);
    position: relative;
}

.news-card:hover {
    transform: translateY(-5px);
    border-color: var(--accent-primary);
    box-shadow: 0 10px 25px var(--shadow-medium);
}

.news-image {
    height: 200px;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    position: relative;
    overflow: hidden;
}

.news-date-overlay {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: var(--border-radius-md);
    font-size: 0.8rem;
    font-weight: 600;
}

.news-content {
    padding: 25px;
}

.news-category {
    color: var(--accent-primary);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 10px;
}

.news-title {
    color: var(--text-primary);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 15px;
    line-height: 1.3;
}

.news-excerpt {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.read-more-btn {
    background: none;
    border: none;
    color: var(--accent-primary);
    font-weight: 500;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.read-more-btn:hover {
    color: var(--accent-secondary);
}

/* Newsletter CTA */
.newsletter-cta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-xl);
    padding: 40px;
}

.newsletter-content h2 {
    color: var(--text-primary);
    font-size: 2rem;
    margin-bottom: 15px;
}

.newsletter-content p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0;
}

.newsletter-form .form-group {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.newsletter-input {
    flex: 1;
    padding: 15px;
    background: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    color: var(--text-primary);
    font-size: 1rem;
}

.newsletter-input:focus {
    border-color: var(--accent-primary);
    outline: none;
}

.newsletter-disclaimer {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin: 0;
}

/* Archive */
.archive-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.archive-month {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 25px;
}

.archive-month h3 {
    color: var(--text-primary);
    margin-bottom: 20px;
    font-size: 1.3rem;
    border-bottom: 2px solid var(--accent-primary);
    padding-bottom: 10px;
}

.archive-list {
    list-style: none;
}

.archive-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.archive-list li:last-child {
    border-bottom: none;
}

.archive-list a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
    flex: 1;
}

.archive-list a:hover {
    color: var(--accent-primary);
}

.archive-date {
    font-size: 0.8rem;
    color: var(--text-muted);
    white-space: nowrap;
}

.load-more-container {
    text-align: center;
    margin-top: 40px;
}

/* Leaderboard Page Styles */
.filters-section {
    background: var(--secondary-bg);
    padding: 30px 0;
    border-bottom: 1px solid var(--border-color);
}

.leaderboard-filters {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.filter-select {
    padding: 10px 15px;
    background: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    color: var(--text-primary);
    font-size: 0.9rem;
    cursor: pointer;
    min-width: 150px;
}

.filter-select:focus {
    border-color: var(--accent-primary);
    outline: none;
}

/* Podium */
.podium {
    display: flex;
    justify-content: center;
    align-items: end;
    gap: 20px;
    margin: 40px 0;
    flex-wrap: wrap;
}

.podium-place {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    transition: transform var(--transition-fast);
}

.podium-place:hover {
    transform: translateY(-5px);
}

.podium-player {
    background: var(--secondary-bg);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    margin-bottom: 15px;
    min-width: 150px;
    transition: all var(--transition-fast);
}

.first .podium-player {
    border-color: #ffd700;
    background: linear-gradient(135deg, var(--secondary-bg), rgba(255, 215, 0, 0.1));
}

.second .podium-player {
    border-color: #c0c0c0;
    background: linear-gradient(135deg, var(--secondary-bg), rgba(192, 192, 192, 0.1));
}

.third .podium-player {
    border-color: #cd7f32;
    background: linear-gradient(135deg, var(--secondary-bg), rgba(205, 127, 50, 0.1));
}

.player-avatar {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.player-name {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.player-score {
    color: var(--accent-primary);
    font-weight: 800;
    font-size: 1.3rem;
}

.podium-base {
    background: var(--tertiary-bg);
    border-radius: var(--border-radius-md);
    padding: 15px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.first .podium-base {
    height: 80px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.second .podium-base {
    height: 60px;
    background: linear-gradient(135deg, #c0c0c0, #e5e5e5);
}

.third .podium-base {
    height: 40px;
    background: linear-gradient(135deg, #cd7f32, #daa520);
}

.place-number {
    font-size: 1.2rem;
    font-weight: 800;
    color: var(--primary-bg);
}

.medal {
    font-size: 1.5rem;
}

/* Leaderboard Table */
.leaderboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.leaderboard-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.leaderboard-table-container {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.leaderboard-table {
    width: 100%;
    border-collapse: collapse;
}

.leaderboard-table th {
    background: var(--tertiary-bg);
    color: var(--text-primary);
    font-weight: 600;
    padding: 15px 20px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.leaderboard-table td {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.leaderboard-table tr:hover {
    background: var(--tertiary-bg);
}

.leaderboard-table tr:last-child td {
    border-bottom: none;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.pagination-btn {
    padding: 10px 20px;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.pagination-btn:hover:not(:disabled) {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    color: var(--primary-bg);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Personal Rankings */
.personal-rankings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.ranking-card {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    transition: all var(--transition-fast);
}

.ranking-card:hover {
    transform: translateY(-3px);
    border-color: var(--accent-primary);
    box-shadow: 0 10px 25px var(--shadow-medium);
}

.ranking-game {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.ranking-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent-primary);
}

/* Achievements Feed */
.achievements-feed {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.achievement-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    transition: all var(--transition-fast);
}

.achievement-item:hover {
    transform: translateX(5px);
    border-color: var(--accent-primary);
}

.achievement-item .achievement-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-md);
    flex-shrink: 0;
}

.achievement-content {
    flex: 1;
}

.achievement-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 5px;
}

.achievement-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.achievement-time {
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* Competition Banner */
.competition-banner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--gradient-primary);
    color: var(--primary-bg);
    border-radius: var(--border-radius-xl);
    padding: 30px;
    gap: 30px;
    flex-wrap: wrap;
}

.competition-content h2 {
    color: var(--primary-bg);
    margin-bottom: 10px;
    font-size: 1.8rem;
}

.competition-content p {
    margin-bottom: 20px;
    opacity: 0.9;
}

.competition-timer {
    display: flex;
    gap: 15px;
}

.timer-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-md);
    padding: 10px 15px;
    min-width: 60px;
}

.timer-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 800;
}

.timer-label {
    font-size: 0.8rem;
    opacity: 0.8;
}

.competition-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

/* Settings Page Styles */
.settings-container {
    max-width: 800px;
    margin: 0 auto;
}

.settings-tabs {
    display: flex;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 5px;
    margin-bottom: 30px;
    overflow-x: auto;
}

.tab-button {
    flex: 1;
    padding: 12px 20px;
    background: transparent;
    border: none;
    border-radius: var(--border-radius-md);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    min-width: 120px;
}

.tab-button:hover {
    background: var(--tertiary-bg);
    color: var(--text-primary);
}

.tab-button.active {
    background: var(--accent-primary);
    color: var(--primary-bg);
}

.tab-content {
    display: none;
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 30px;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

.settings-section {
    margin-bottom: 40px;
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section h3 {
    color: var(--text-primary);
    margin-bottom: 20px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-icon {
    font-size: 1.5rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info {
    flex: 1;
    margin-right: 20px;
}

.setting-label {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 5px;
    font-size: 1rem;
}

.setting-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.setting-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Form Controls */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-input,
.form-select {
    width: 100%;
    padding: 12px 15px;
    background: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    color: var(--text-primary);
    font-size: 1rem;
    transition: border-color var(--transition-fast);
}

.form-input:focus,
.form-select:focus {
    border-color: var(--accent-primary);
    outline: none;
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    width: 50px;
    height: 26px;
    background: var(--border-color);
    border-radius: 13px;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.toggle-switch.active {
    background: var(--accent-primary);
}

.toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 22px;
    height: 22px;
    background: white;
    border-radius: 50%;
    transition: transform var(--transition-fast);
}

.toggle-switch.active .toggle-slider {
    transform: translateX(24px);
}

/* Range Slider */
.range-slider {
    width: 100%;
    height: 6px;
    background: var(--border-color);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--accent-primary);
    border-radius: 50%;
    cursor: pointer;
}

.range-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: var(--accent-primary);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.range-value {
    color: var(--accent-primary);
    font-weight: 600;
    margin-left: 10px;
    min-width: 40px;
    text-align: center;
}

/* Key Binding */
.key-binding {
    display: flex;
    align-items: center;
    gap: 10px;
}

.key-display {
    background: var(--tertiary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: 8px 12px;
    min-width: 60px;
    text-align: center;
    font-family: monospace;
    font-weight: 600;
    color: var(--text-primary);
}

.key-change-btn {
    padding: 6px 12px;
    background: var(--accent-primary);
    border: none;
    border-radius: var(--border-radius-md);
    color: var(--primary-bg);
    font-size: 0.8rem;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.key-change-btn:hover {
    background: var(--accent-secondary);
}

/* Color Picker */
.color-picker {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-preview {
    width: 40px;
    height: 40px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: border-color var(--transition-fast);
}

.color-preview:hover {
    border-color: var(--accent-primary);
}

.color-input {
    opacity: 0;
    position: absolute;
    pointer-events: none;
}

/* Settings Actions */
.settings-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

/* Character Counter */
.character-counter {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-align: right;
    margin-top: 5px;
}

.character-counter.warning {
    color: var(--warning);
}

.character-counter.error {
    color: var(--error);
}

/* Search Highlight */
.search-highlight {
    background: rgba(0, 212, 255, 0.2);
    border: 1px solid var(--accent-primary);
    border-radius: var(--border-radius-md);
}

.search-results {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    margin-bottom: 30px;
}

.search-results-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.search-no-results {
    text-align: center;
    padding: 40px 20px;
}

.search-no-results h3 {
    color: var(--text-primary);
    margin-bottom: 10px;
}

.search-no-results p {
    color: var(--text-secondary);
    margin: 0;
}

/* Responsive Design for Pages */
@media (max-width: 768px) {
    /* Page Headers */
    .page-header {
        padding: 80px 0 40px;
    }

    .page-title {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 10px;
    }

    .title-icon {
        font-size: 3rem;
    }

    .page-subtitle {
        font-size: 1rem;
    }

    .page-stats {
        flex-direction: column;
        gap: 15px;
    }

    /* Hero Section */
    .hero {
        padding: 100px 0 60px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        flex-direction: column;
        gap: 20px;
    }

    /* Profile Page */
    .profile-info {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .profile-avatar {
        margin: 0 auto 15px;
    }

    .profile-name {
        font-size: 2rem;
    }

    .profile-actions {
        flex-direction: column;
        width: 100%;
    }

    .profile-stats-grid {
        grid-template-columns: 1fr;
    }

    .game-performance-grid {
        grid-template-columns: 1fr;
    }

    .performance-stats {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .achievements-grid {
        grid-template-columns: 1fr;
    }

    .achievement-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    /* News Page */
    .featured-article {
        grid-template-columns: 1fr;
        gap: 25px;
        padding: 25px;
    }

    .featured-article h2 {
        font-size: 1.8rem;
    }

    .featured-meta {
        flex-direction: column;
        gap: 10px;
    }

    .news-categories {
        gap: 10px;
    }

    .category-btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .news-header {
        flex-direction: column;
        align-items: stretch;
    }

    .news-grid {
        grid-template-columns: 1fr;
    }

    .newsletter-cta {
        grid-template-columns: 1fr;
        gap: 25px;
        padding: 25px;
    }

    .newsletter-form .form-group {
        flex-direction: column;
    }

    .archive-grid {
        grid-template-columns: 1fr;
    }

    /* Leaderboard Page */
    .leaderboard-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        width: 100%;
    }

    .filter-select {
        min-width: auto;
    }

    .podium {
        flex-direction: column;
        align-items: center;
    }

    .podium-place {
        width: 100%;
        max-width: 250px;
    }

    .leaderboard-table-container {
        overflow-x: auto;
    }

    .leaderboard-table {
        min-width: 600px;
    }

    .pagination {
        flex-direction: column;
        gap: 15px;
    }

    .personal-rankings {
        grid-template-columns: 1fr;
    }

    .ranking-stats {
        grid-template-columns: 1fr;
    }

    .achievements-feed {
        gap: 10px;
    }

    .achievement-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .competition-banner {
        flex-direction: column;
        text-align: center;
        gap: 20px;
        padding: 25px;
    }

    .competition-timer {
        justify-content: center;
    }

    /* Help Page */
    .quick-help-grid {
        grid-template-columns: 1fr;
    }

    .tutorials-grid {
        grid-template-columns: 1fr;
    }

    .troubleshooting-sections {
        gap: 25px;
    }

    .trouble-solutions {
        grid-template-columns: 1fr;
    }

    .faq-grid {
        gap: 10px;
    }

    .support-actions {
        flex-direction: column;
    }

    /* Settings Page */
    .settings-tabs {
        flex-direction: column;
        padding: 0;
        background: transparent;
        border: none;
    }

    .tab-button {
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-md);
        margin-bottom: 5px;
        min-width: auto;
    }

    .tab-content {
        padding: 20px;
    }

    .setting-item {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
        padding: 15px 0;
    }

    .setting-info {
        margin-right: 0;
    }

    .setting-control {
        justify-content: space-between;
    }

    .settings-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .key-binding {
        flex-wrap: wrap;
    }

    .range-slider {
        margin-bottom: 10px;
    }

    /* Form Elements */
    .form-row {
        flex-direction: column;
    }

    .newsletter-input {
        margin-bottom: 10px;
    }

    /* General Mobile Adjustments */
    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .stat-icon {
        margin: 0 auto;
    }

    .help-card,
    .tutorial-card,
    .performance-card,
    .ranking-card {
        padding: 20px;
    }

    .game-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .tutorial-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    /* Extra small screens */
    .page-title {
        font-size: 2rem;
    }

    .title-icon {
        font-size: 2.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .profile-name {
        font-size: 1.8rem;
    }

    .featured-article h2 {
        font-size: 1.5rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .competition-banner {
        padding: 20px;
    }

    .competition-timer {
        flex-wrap: wrap;
        gap: 10px;
    }

    .timer-item {
        min-width: 50px;
        padding: 8px 10px;
    }

    .timer-value {
        font-size: 1.2rem;
    }

    .podium-player {
        padding: 15px;
        min-width: 120px;
    }

    .player-avatar {
        font-size: 2rem;
    }

    .achievement-card,
    .performance-card,
    .help-card,
    .tutorial-card {
        padding: 15px;
    }
}
