// Leaderboards Page Functionality
class LeaderboardsPage {
    constructor() {
        this.currentGame = 'all';
        this.currentTimeframe = 'all-time';
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.leaderboardData = {};
        this.personalRankings = {};
        this.tournaments = [];
        this.subscriptions = [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadLeaderboards();
        this.loadPersonalRankings();
        this.loadTournaments();
        this.setupRealTimeUpdates();
    }

    setupEventListeners() {
        // Filter controls
        const gameFilter = document.getElementById('game-filter');
        const timeFilter = document.getElementById('time-filter');
        
        if (gameFilter) {
            gameFilter.addEventListener('change', (e) => {
                this.currentGame = e.target.value;
                this.loadLeaderboards();
            });
        }
        
        if (timeFilter) {
            timeFilter.addEventListener('change', (e) => {
                this.currentTimeframe = e.target.value;
                this.loadLeaderboards();
            });
        }

        // Pagination
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                if (this.currentPage > 1) {
                    this.currentPage--;
                    this.renderLeaderboard();
                }
            });
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                this.currentPage++;
                this.renderLeaderboard();
            });
        }

        // Tournament join buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('join-tournament-btn')) {
                const tournamentId = e.target.dataset.tournamentId;
                this.joinTournament(tournamentId);
            }
        });
    }

    async loadLeaderboards() {
        this.showLoading(true);
        
        try {
            if (window.supabaseClient) {
                // Load from Supabase
                this.leaderboardData = await window.supabaseClient.getLeaderboard(
                    this.currentGame === 'all' ? null : this.currentGame,
                    50 // Load top 50
                );
            } else {
                // Fallback to mock data
                this.leaderboardData = this.getMockLeaderboard();
            }
            
            this.renderPodium();
            this.renderLeaderboard();
        } catch (error) {
            console.error('Error loading leaderboards:', error);
            this.leaderboardData = this.getMockLeaderboard();
            this.renderPodium();
            this.renderLeaderboard();
        } finally {
            this.showLoading(false);
        }
    }

    getMockLeaderboard() {
        return [
            { rank: 1, username: 'ProGamer2024', score: 15420, game_id: 'snake', played_at: '2024-12-15', avatar: '🏆' },
            { rank: 2, username: 'SnakeMaster', score: 14890, game_id: 'snake', played_at: '2024-12-14', avatar: '🐍' },
            { rank: 3, username: 'TetrisKing', score: 14350, game_id: 'tetris', played_at: '2024-12-13', avatar: '🧩' },
            { rank: 4, username: 'PongChamp', score: 13920, game_id: 'pong', played_at: '2024-12-12', avatar: '🏓' },
            { rank: 5, username: 'ArcadeHero', score: 13500, game_id: 'snake', played_at: '2024-12-11', avatar: '🎮' },
            { rank: 6, username: 'RetroGamer', score: 13200, game_id: 'tetris', played_at: '2024-12-10', avatar: '👾' },
            { rank: 7, username: 'SpeedRunner', score: 12800, game_id: 'pong', played_at: '2024-12-09', avatar: '⚡' },
            { rank: 8, username: 'PixelPro', score: 12450, game_id: 'snake', played_at: '2024-12-08', avatar: '🎯' },
            { rank: 9, username: 'GameMaster', score: 12100, game_id: 'tetris', played_at: '2024-12-07', avatar: '🌟' },
            { rank: 10, username: 'ClassicFan', score: 11750, game_id: 'pong', played_at: '2024-12-06', avatar: '🎲' }
        ];
    }

    renderPodium() {
        const podium = document.querySelector('.podium');
        if (!podium || this.leaderboardData.length < 3) return;

        const top3 = this.leaderboardData.slice(0, 3);
        const podiumHTML = `
            <div class="podium-place second">
                <div class="podium-player">
                    <div class="player-avatar">${top3[1]?.avatar || '🥈'}</div>
                    <div class="player-name">${top3[1]?.username || 'N/A'}</div>
                    <div class="player-score">${this.formatScore(top3[1]?.score || 0)}</div>
                </div>
                <div class="podium-base">
                    <div class="place-number">2</div>
                    <div class="medal">🥈</div>
                </div>
            </div>
            <div class="podium-place first">
                <div class="podium-player">
                    <div class="player-avatar">${top3[0]?.avatar || '🥇'}</div>
                    <div class="player-name">${top3[0]?.username || 'N/A'}</div>
                    <div class="player-score">${this.formatScore(top3[0]?.score || 0)}</div>
                </div>
                <div class="podium-base">
                    <div class="place-number">1</div>
                    <div class="medal">🥇</div>
                </div>
            </div>
            <div class="podium-place third">
                <div class="podium-player">
                    <div class="player-avatar">${top3[2]?.avatar || '🥉'}</div>
                    <div class="player-name">${top3[2]?.username || 'N/A'}</div>
                    <div class="player-score">${this.formatScore(top3[2]?.score || 0)}</div>
                </div>
                <div class="podium-base">
                    <div class="place-number">3</div>
                    <div class="medal">🥉</div>
                </div>
            </div>
        `;
        
        podium.innerHTML = podiumHTML;
    }

    renderLeaderboard() {
        const tableBody = document.querySelector('.leaderboard-table tbody');
        if (!tableBody) return;

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageData = this.leaderboardData.slice(startIndex, endIndex);

        if (pageData.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center">No data available</td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = pageData.map((entry, index) => {
            const rank = startIndex + index + 1;
            const gameIcon = this.getGameIcon(entry.game_id);
            const playedDate = new Date(entry.played_at).toLocaleDateString();
            
            return `
                <tr class="${rank <= 3 ? 'top-rank' : ''}">
                    <td>
                        <span class="rank-number">${rank}</span>
                        ${rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : ''}
                    </td>
                    <td>
                        <div class="player-info">
                            <span class="player-avatar">${entry.avatar || '👤'}</span>
                            <span class="player-name">${entry.username}</span>
                        </div>
                    </td>
                    <td>
                        <div class="game-info">
                            <span class="game-icon">${gameIcon}</span>
                            <span class="game-name">${this.getGameName(entry.game_id)}</span>
                        </div>
                    </td>
                    <td class="score-cell">${this.formatScore(entry.score)}</td>
                    <td class="date-cell">${playedDate}</td>
                </tr>
            `;
        }).join('');

        this.updatePagination();
    }

    updatePagination() {
        const totalPages = Math.ceil(this.leaderboardData.length / this.itemsPerPage);
        const paginationInfo = document.querySelector('.pagination-info');
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');

        if (paginationInfo) {
            const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
            const endItem = Math.min(this.currentPage * this.itemsPerPage, this.leaderboardData.length);
            paginationInfo.textContent = `Showing ${startItem}-${endItem} of ${this.leaderboardData.length} entries`;
        }

        if (prevBtn) {
            prevBtn.disabled = this.currentPage === 1;
        }

        if (nextBtn) {
            nextBtn.disabled = this.currentPage >= totalPages;
        }
    }

    async loadPersonalRankings() {
        try {
            if (window.supabaseClient && window.supabaseClient.isAuthenticated()) {
                this.personalRankings = await window.supabaseClient.getUserRankings();
            } else {
                this.personalRankings = this.getMockPersonalRankings();
            }
            
            this.renderPersonalRankings();
        } catch (error) {
            console.error('Error loading personal rankings:', error);
            this.personalRankings = this.getMockPersonalRankings();
            this.renderPersonalRankings();
        }
    }

    getMockPersonalRankings() {
        return {
            snake: { rank: 15, score: 8750, total_players: 1250 },
            tetris: { rank: 23, score: 6420, total_players: 980 },
            pong: { rank: 8, score: 9200, total_players: 750 }
        };
    }

    renderPersonalRankings() {
        const personalRankings = document.querySelector('.personal-rankings');
        if (!personalRankings) return;

        const games = ['snake', 'tetris', 'pong'];
        personalRankings.innerHTML = games.map(gameId => {
            const ranking = this.personalRankings[gameId];
            if (!ranking) return '';

            const percentile = Math.round((1 - ranking.rank / ranking.total_players) * 100);
            
            return `
                <div class="ranking-card">
                    <div class="ranking-game">
                        <div class="game-icon">${this.getGameIcon(gameId)}</div>
                        <div class="game-info">
                            <h3>${this.getGameName(gameId)}</h3>
                            <p>Your performance</p>
                        </div>
                    </div>
                    <div class="ranking-stats">
                        <div class="stat">
                            <span class="stat-label">Rank</span>
                            <span class="stat-value">#${ranking.rank}</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Best Score</span>
                            <span class="stat-value">${this.formatScore(ranking.score)}</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Percentile</span>
                            <span class="stat-value">${percentile}%</span>
                        </div>
                    </div>
                    <div class="ranking-actions">
                        <button type="button" class="btn btn-primary" onclick="playGame('${gameId}')">
                            Play Now
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    async loadTournaments() {
        try {
            if (window.supabaseClient) {
                this.tournaments = await window.supabaseClient.getTournaments();
            } else {
                this.tournaments = this.getMockTournaments();
            }
            
            this.renderTournaments();
        } catch (error) {
            console.error('Error loading tournaments:', error);
            this.tournaments = this.getMockTournaments();
            this.renderTournaments();
        }
    }

    getMockTournaments() {
        return [
            {
                id: '1',
                name: 'Weekly Snake Championship',
                description: 'Compete for the highest score in Snake!',
                game_id: 'snake',
                start_date: new Date().toISOString(),
                end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                participant_count: 42,
                prize_pool: { first: '🏆 Champion Badge', second: '🥈 Runner-up Badge', third: '🥉 Participant Badge' }
            }
        ];
    }

    renderTournaments() {
        const competitionBanner = document.querySelector('.competition-banner');
        if (!competitionBanner || this.tournaments.length === 0) return;

        const tournament = this.tournaments[0]; // Show the first active tournament
        const endDate = new Date(tournament.end_date);
        const timeLeft = endDate - new Date();
        
        if (timeLeft <= 0) return; // Tournament ended

        const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));

        competitionBanner.innerHTML = `
            <div class="competition-content">
                <h2>${tournament.name}</h2>
                <p>${tournament.description}</p>
                <p><strong>${tournament.participant_count}</strong> players competing</p>
                <button type="button" class="btn btn-secondary join-tournament-btn" data-tournament-id="${tournament.id}">
                    Join Tournament
                </button>
            </div>
            <div class="competition-timer">
                <div class="timer-item">
                    <span class="timer-value">${days}</span>
                    <span class="timer-label">Days</span>
                </div>
                <div class="timer-item">
                    <span class="timer-value">${hours}</span>
                    <span class="timer-label">Hours</span>
                </div>
                <div class="timer-item">
                    <span class="timer-value">${minutes}</span>
                    <span class="timer-label">Minutes</span>
                </div>
            </div>
        `;

        // Update timer every minute
        setInterval(() => {
            this.updateTournamentTimer();
        }, 60000);
    }

    updateTournamentTimer() {
        if (this.tournaments.length === 0) return;
        
        const tournament = this.tournaments[0];
        const endDate = new Date(tournament.end_date);
        const timeLeft = endDate - new Date();
        
        if (timeLeft <= 0) {
            this.loadTournaments(); // Reload to get new tournaments
            return;
        }

        const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));

        const timerItems = document.querySelectorAll('.timer-value');
        if (timerItems.length >= 3) {
            timerItems[0].textContent = days;
            timerItems[1].textContent = hours;
            timerItems[2].textContent = minutes;
        }
    }

    async joinTournament(tournamentId) {
        try {
            if (!window.supabaseClient || !window.supabaseClient.isAuthenticated()) {
                this.showNotification('Please sign in to join tournaments', 'warning');
                return;
            }

            await window.supabaseClient.joinTournament(tournamentId);
            this.showNotification('Successfully joined tournament!', 'success');
            this.loadTournaments(); // Refresh tournament data
        } catch (error) {
            console.error('Error joining tournament:', error);
            this.showNotification('Failed to join tournament. Please try again.', 'error');
        }
    }

    setupRealTimeUpdates() {
        if (!window.supabaseClient) return;

        // Subscribe to leaderboard updates
        const subscription = window.supabaseClient.subscribeToLeaderboard(
            this.currentGame === 'all' ? null : this.currentGame,
            (payload) => {
                console.log('Leaderboard updated:', payload);
                this.loadLeaderboards(); // Refresh leaderboard
            }
        );

        if (subscription) {
            this.subscriptions.push(subscription);
        }
    }

    // Utility methods
    getGameIcon(gameId) {
        const icons = {
            snake: '🐍',
            tetris: '🧩',
            pong: '🏓',
            'space-invaders': '🚀'
        };
        return icons[gameId] || '🎮';
    }

    getGameName(gameId) {
        const names = {
            snake: 'Snake',
            tetris: 'Tetris',
            pong: 'Pong',
            'space-invaders': 'Space Invaders'
        };
        return names[gameId] || gameId;
    }

    formatScore(score) {
        if (score >= 1000000) {
            return (score / 1000000).toFixed(1) + 'M';
        } else if (score >= 1000) {
            return (score / 1000).toFixed(1) + 'k';
        }
        return score.toLocaleString();
    }

    showLoading(show) {
        const loadingElements = document.querySelectorAll('.loading-state');
        const contentElements = document.querySelectorAll('.leaderboard-table, .podium');
        
        loadingElements.forEach(el => {
            el.style.display = show ? 'block' : 'none';
        });
        
        contentElements.forEach(el => {
            el.style.opacity = show ? '0.5' : '1';
        });
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button type="button" class="notification-close">&times;</button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);

        // Close button
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });
    }

    destroy() {
        // Unsubscribe from real-time updates
        this.subscriptions.forEach(subscription => {
            if (window.supabaseClient) {
                window.supabaseClient.unsubscribe(subscription);
            }
        });
        this.subscriptions = [];
    }
}

// Global function to play games
function playGame(gameId) {
    window.location.href = `games.html?play=${gameId}`;
}

// Initialize leaderboards page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.body.contains(document.querySelector('.leaderboard-table'))) {
        window.leaderboardsPage = new LeaderboardsPage();
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.leaderboardsPage) {
        window.leaderboardsPage.destroy();
    }
});
