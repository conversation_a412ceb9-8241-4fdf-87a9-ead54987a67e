// Help Page Functionality
class HelpPage {
    constructor() {
        this.faqItems = [];
        this.init();
    }

    init() {
        this.setupFAQ();
        this.setupSmoothScrolling();
        this.setupAnimations();
        this.trackHelpUsage();
    }

    setupFAQ() {
        this.faqItems = document.querySelectorAll('.faq-item');
        
        this.faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');
            const answer = item.querySelector('.faq-answer');
            const icon = item.querySelector('.faq-icon');
            
            if (!question || !answer || !icon) return;
            
            question.addEventListener('click', () => {
                const isExpanded = question.getAttribute('aria-expanded') === 'true';
                
                // Close all other FAQ items
                this.faqItems.forEach(otherItem => {
                    if (otherItem !== item) {
                        const otherQuestion = otherItem.querySelector('.faq-question');
                        const otherAnswer = otherItem.querySelector('.faq-answer');
                        const otherIcon = otherItem.querySelector('.faq-icon');
                        
                        if (otherQuestion && otherAnswer && otherIcon) {
                            otherQuestion.setAttribute('aria-expanded', 'false');
                            otherAnswer.style.maxHeight = '0';
                            otherIcon.textContent = '+';
                            otherItem.classList.remove('active');
                        }
                    }
                });
                
                // Toggle current item
                if (isExpanded) {
                    question.setAttribute('aria-expanded', 'false');
                    answer.style.maxHeight = '0';
                    icon.textContent = '+';
                    item.classList.remove('active');
                } else {
                    question.setAttribute('aria-expanded', 'true');
                    answer.style.maxHeight = answer.scrollHeight + 'px';
                    icon.textContent = '−';
                    item.classList.add('active');
                    
                    // Track FAQ interaction
                    this.trackFAQClick(question.textContent.trim());
                }
            });

            // Keyboard navigation
            question.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    question.click();
                }
            });
        });
    }

    setupSmoothScrolling() {
        // Handle anchor links for smooth scrolling
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        
        anchorLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                if (href === '#') return;
                
                const target = document.querySelector(href);
                if (target) {
                    e.preventDefault();
                    
                    const offsetTop = target.offsetTop - 100; // Account for fixed navbar
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                    
                    // Update URL without triggering scroll
                    history.pushState(null, null, href);
                }
            });
        });

        // Handle initial hash on page load
        if (window.location.hash) {
            setTimeout(() => {
                const target = document.querySelector(window.location.hash);
                if (target) {
                    const offsetTop = target.offsetTop - 100;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            }, 100);
        }
    }

    setupAnimations() {
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe help cards
        const helpCards = document.querySelectorAll('.help-card');
        helpCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in');
            observer.observe(card);
        });

        // Observe tutorial cards
        const tutorialCards = document.querySelectorAll('.tutorial-card');
        tutorialCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.15}s`;
            card.classList.add('slide-up');
            observer.observe(card);
        });

        // Observe troubleshooting sections
        const troubleSections = document.querySelectorAll('.trouble-section');
        troubleSections.forEach((section, index) => {
            section.style.animationDelay = `${index * 0.1}s`;
            section.classList.add('fade-in');
            observer.observe(section);
        });

        // Observe FAQ items
        this.faqItems.forEach((item, index) => {
            item.style.animationDelay = `${index * 0.05}s`;
            item.classList.add('slide-up');
            observer.observe(item);
        });
    }

    trackHelpUsage() {
        // Track which help sections are most viewed
        const sections = document.querySelectorAll('section[id]');
        const sectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                    this.trackSectionView(entry.target.id);
                }
            });
        }, {
            threshold: 0.5
        });

        sections.forEach(section => {
            sectionObserver.observe(section);
        });

        // Track help link clicks
        const helpLinks = document.querySelectorAll('.help-link');
        helpLinks.forEach(link => {
            link.addEventListener('click', () => {
                this.trackHelpLinkClick(link.textContent.trim(), link.href);
            });
        });

        // Track tutorial button clicks
        const tutorialButtons = document.querySelectorAll('.tutorial-card .btn');
        tutorialButtons.forEach(button => {
            button.addEventListener('click', () => {
                const gameName = button.closest('.tutorial-card').querySelector('h3').textContent;
                this.trackTutorialPlay(gameName);
            });
        });
    }

    trackSectionView(sectionId) {
        // Track section views for analytics
        const viewData = {
            section: sectionId,
            timestamp: new Date().toISOString(),
            page: 'help'
        };

        // Save to localStorage for analytics
        const views = JSON.parse(localStorage.getItem('help-section-views') || '[]');
        views.push(viewData);
        
        // Keep only last 100 views
        if (views.length > 100) {
            views.splice(0, views.length - 100);
        }
        
        localStorage.setItem('help-section-views', JSON.stringify(views));
    }

    trackFAQClick(question) {
        const faqData = {
            question: question,
            timestamp: new Date().toISOString(),
            page: 'help'
        };

        const faqClicks = JSON.parse(localStorage.getItem('faq-clicks') || '[]');
        faqClicks.push(faqData);
        
        // Keep only last 50 clicks
        if (faqClicks.length > 50) {
            faqClicks.splice(0, faqClicks.length - 50);
        }
        
        localStorage.setItem('faq-clicks', JSON.stringify(faqClicks));
    }

    trackHelpLinkClick(linkText, href) {
        const linkData = {
            text: linkText,
            href: href,
            timestamp: new Date().toISOString(),
            page: 'help'
        };

        const linkClicks = JSON.parse(localStorage.getItem('help-link-clicks') || '[]');
        linkClicks.push(linkData);
        
        // Keep only last 50 clicks
        if (linkClicks.length > 50) {
            linkClicks.splice(0, linkClicks.length - 50);
        }
        
        localStorage.setItem('help-link-clicks', JSON.stringify(linkClicks));
    }

    trackTutorialPlay(gameName) {
        const tutorialData = {
            game: gameName,
            timestamp: new Date().toISOString(),
            page: 'help'
        };

        const tutorialPlays = JSON.parse(localStorage.getItem('tutorial-plays') || '[]');
        tutorialPlays.push(tutorialData);
        
        // Keep only last 50 plays
        if (tutorialPlays.length > 50) {
            tutorialPlays.splice(0, tutorialPlays.length - 50);
        }
        
        localStorage.setItem('tutorial-plays', JSON.stringify(tutorialPlays));
    }

    // Search functionality for help content
    setupSearch() {
        const searchInput = document.getElementById('help-search');
        if (!searchInput) return;

        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performSearch(e.target.value.trim());
            }, 300);
        });
    }

    performSearch(query) {
        if (!query) {
            this.clearSearchResults();
            return;
        }

        const searchableElements = document.querySelectorAll('.help-card, .tutorial-card, .faq-item, .trouble-section');
        const results = [];

        searchableElements.forEach(element => {
            const text = element.textContent.toLowerCase();
            if (text.includes(query.toLowerCase())) {
                results.push(element);
                element.classList.add('search-highlight');
            } else {
                element.classList.remove('search-highlight');
            }
        });

        this.displaySearchResults(results, query);
    }

    displaySearchResults(results, query) {
        // Create or update search results summary
        let resultsContainer = document.getElementById('search-results');
        if (!resultsContainer) {
            resultsContainer = document.createElement('div');
            resultsContainer.id = 'search-results';
            resultsContainer.className = 'search-results';
            
            const firstSection = document.querySelector('.section');
            if (firstSection) {
                firstSection.parentNode.insertBefore(resultsContainer, firstSection);
            }
        }

        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="container">
                    <div class="search-no-results">
                        <h3>No results found for "${query}"</h3>
                        <p>Try different keywords or browse the sections below.</p>
                    </div>
                </div>
            `;
        } else {
            resultsContainer.innerHTML = `
                <div class="container">
                    <div class="search-results-summary">
                        <h3>Found ${results.length} result${results.length !== 1 ? 's' : ''} for "${query}"</h3>
                        <button type="button" class="btn btn-secondary" onclick="helpPage.clearSearchResults()">Clear Search</button>
                    </div>
                </div>
            `;
        }

        resultsContainer.style.display = 'block';
    }

    clearSearchResults() {
        const resultsContainer = document.getElementById('search-results');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }

        const searchInput = document.getElementById('help-search');
        if (searchInput) {
            searchInput.value = '';
        }

        // Remove search highlights
        const highlightedElements = document.querySelectorAll('.search-highlight');
        highlightedElements.forEach(element => {
            element.classList.remove('search-highlight');
        });
    }

    // Get help analytics data
    getHelpAnalytics() {
        return {
            sectionViews: JSON.parse(localStorage.getItem('help-section-views') || '[]'),
            faqClicks: JSON.parse(localStorage.getItem('faq-clicks') || '[]'),
            linkClicks: JSON.parse(localStorage.getItem('help-link-clicks') || '[]'),
            tutorialPlays: JSON.parse(localStorage.getItem('tutorial-plays') || '[]')
        };
    }

    // Export help data for support team
    exportHelpData() {
        const data = this.getHelpAnalytics();
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `help-analytics-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// Game play function for tutorial buttons
function playGame(gameId) {
    // Track tutorial to game conversion
    if (window.helpPage) {
        window.helpPage.trackTutorialPlay(gameId);
    }
    
    // Add to recently played
    let recentlyPlayed = JSON.parse(localStorage.getItem('recently-played') || '[]');
    recentlyPlayed = recentlyPlayed.filter(id => id !== gameId);
    recentlyPlayed.unshift(gameId);
    recentlyPlayed = recentlyPlayed.slice(0, 10);
    localStorage.setItem('recently-played', JSON.stringify(recentlyPlayed));

    // Redirect to games page with game ID
    window.location.href = `games.html?play=${gameId}`;
}

// Initialize help page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.body.contains(document.querySelector('.help-card'))) {
        window.helpPage = new HelpPage();
    }
});
