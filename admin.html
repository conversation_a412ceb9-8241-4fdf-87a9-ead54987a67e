<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="GameHub Admin Dashboard - Manage users, content, and analytics">
    <meta name="keywords" content="admin, dashboard, management, analytics">
    <meta name="author" content="GameHub Team">
    <title>Admin Dashboard - GameHub</title>
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/pages.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body class="admin-body">
    <!-- Admin Navigation -->
    <nav class="admin-nav">
        <div class="admin-nav-header">
            <div class="nav-brand">
                <span class="nav-logo">🎮</span>
                <span class="nav-title">GameHub Admin</span>
            </div>
 <button type="button" class="nav-toggle" id="admin-nav-toggle" title="Toggle Navigation">
                <span class="hamburger"></span>
            </button>
        </div>
        <div class="admin-nav-menu" id="admin-nav-menu">
            <ul class="nav-sections">
                <li class="nav-section">
                    <h3>Overview</h3>
                    <ul>
                        <li><a href="#dashboard" class="nav-link active" data-section="dashboard">📊 Dashboard</a></li>
                        <li><a href="#analytics" class="nav-link" data-section="analytics">📈 Analytics</a></li>
                    </ul>
                </li>
                <li class="nav-section">
                    <h3>Management</h3>
                    <ul>
                        <li><a href="#users" class="nav-link" data-section="users">👥 Users</a></li>
                        <li><a href="#content" class="nav-link" data-section="content">📝 Content</a></li>
                        <li><a href="#tournaments" class="nav-link" data-section="tournaments">🏆 Tournaments</a></li>
                        <li><a href="#moderation" class="nav-link" data-section="moderation">🛡️ Moderation</a></li>
                    </ul>
                </li>
                <li class="nav-section">
                    <h3>System</h3>
                    <ul>
                        <li><a href="#settings" class="nav-link" data-section="settings">⚙️ Settings</a></li>
                        <li><a href="#logs" class="nav-link" data-section="logs">📋 Logs</a></li>
                    </ul>
                </li>
            </ul>
            <div class="nav-footer">
                <div class="admin-user">
                    <div class="admin-avatar">👤</div>
                    <div class="admin-info">
                        <div class="admin-name" id="admin-name">Admin User</div>
                        <div class="admin-role">Administrator</div>
                    </div>
                </div>
                <div class="nav-actions">
                    <a href="index.html" class="btn btn-secondary btn-sm">🏠 Back to Site</a>
 <button type="button" class="btn btn-danger btn-sm" id="admin-logout" title="Logout">🚪 Logout</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="admin-main">
        <!-- Dashboard Section -->
        <section class="admin-section active" id="dashboard-section">
            <div class="section-header">
                <h1>Dashboard Overview</h1>
 <div class="header-actions" role="group" aria-label="Dashboard Actions">
                    <button type="button" class="btn btn-primary" id="refresh-dashboard">🔄 Refresh</button>
                    <select class="time-filter" id="dashboard-timeframe">
                        <option value="24h">Last 24 Hours</option>
                        <option value="7d" selected>Last 7 Days</option>
                        <option value="30d">Last 30 Days</option>
                        <option value="90d">Last 90 Days</option>
                    </select>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <div class="stat-value" id="total-users">0</div>
                        <div class="stat-label">Total Users</div>
                        <div class="stat-change positive" id="users-change">+0%</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🎮</div>
                    <div class="stat-content">
                        <div class="stat-value" id="total-games-played">0</div>
                        <div class="stat-label">Games Played</div>
                        <div class="stat-change positive" id="games-change">+0%</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🏆</div>
                    <div class="stat-content">
                        <div class="stat-value" id="active-tournaments">0</div>
                        <div class="stat-label">Active Tournaments</div>
                        <div class="stat-change neutral" id="tournaments-change">0</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💬</div>
                    <div class="stat-content">
                        <div class="stat-value" id="discord-members">0</div>
                        <div class="stat-label">Discord Members</div>
                        <div class="stat-change positive" id="discord-change">+0%</div>
                    </div>
                </div>
            </div>

            <!-- Charts and Recent Activity -->
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>User Activity</h3>
                        <select class="chart-filter" id="activity-filter">
                            <option value="registrations">New Registrations</option>
                            <option value="logins">Daily Logins</option>
                            <option value="games">Games Played</option>
                        </select>
                    </div>
                    <div class="card-content">
                        <canvas id="activity-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Recent Activity</h3>
 <button type="button" class="btn btn-sm btn-secondary" id="refresh-activity" title="Refresh Activity">🔄</button>
                    </div>
                    <div class="card-content">
                        <div class="activity-feed" id="recent-activity">
                            <!-- Activity items will be loaded here -->
                        </div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Top Games</h3>
                        <span class="card-subtitle">Most played this week</span>
                    </div>
                    <div class="card-content">
                        <div class="top-games-list" id="top-games">
                            <!-- Top games will be loaded here -->
                        </div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>System Status</h3>
                        <div class="status-indicator online" id="system-status">🟢 Online</div>
                    </div>
                    <div class="card-content">
                        <div class="system-metrics" id="system-metrics">
                            <!-- System metrics will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Analytics Section -->
        <section class="admin-section" id="analytics-section">
            <div class="section-header">
                <h1>Analytics & Reports</h1>
 <div class="header-actions" role="group" aria-label="Analytics Actions">
                    <button type="button" class="btn btn-primary" id="export-analytics">📊 Export Report</button>
                    <button type="button" class="btn btn-secondary" id="refresh-analytics">🔄 Refresh</button>
                </div>
            </div>

            <!-- Analytics Content -->
            <div class="analytics-grid">
                <div class="analytics-card full-width">
                    <div class="card-header">
                        <h3>User Engagement Metrics</h3>
                        <div class="metric-filters">
                            <select id="engagement-timeframe">
                                <option value="7d">Last 7 Days</option>
                                <option value="30d" selected>Last 30 Days</option>
                                <option value="90d">Last 90 Days</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-content">
                        <canvas id="engagement-chart" width="800" height="300"></canvas>
                    </div>
                </div>

                <div class="analytics-card">
                    <div class="card-header">
                        <h3>Game Performance</h3>
                    </div>
                    <div class="card-content">
                        <div class="game-stats" id="game-performance">
                            <!-- Game performance stats -->
                        </div>
                    </div>
                </div>

                <div class="analytics-card">
                    <div class="card-header">
                        <h3>User Demographics</h3>
                    </div>
                    <div class="card-content">
                        <canvas id="demographics-chart" width="300" height="300"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- Users Section -->
        <section class="admin-section" id="users-section">
            <div class="section-header">
                <h1>User Management</h1>
 <div class="header-actions" role="group" aria-label="User Management Actions">
                    <button type="button" class="btn btn-primary" id="add-user">➕ Add User</button>
                    <button type="button" class="btn btn-secondary" id="export-users">📊 Export</button>
                </div>
            </div>

            <!-- User Filters -->
            <div class="filters-bar">
                <div class="search-box">
                    <input type="text" placeholder="Search users..." id="user-search" class="search-input">
                    <button type="button" class="search-btn">🔍</button>
 </div>
                <select id="user-role-filter">
                    <option value="">All Roles</option>
                    <option value="admin">Admin</option>
                    <option value="moderator">Moderator</option>
                    <option value="user">User</option>
                    <option value="banned">Banned</option>
                </select>
                <select id="user-status-filter">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="suspended">Suspended</option>
                </select>
            </div>

            <!-- Users Table -->
            <div class="table-container">
                <table class="admin-table" id="users-table">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Joined</th>
                            <th>Last Active</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <!-- Users will be loaded here -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <div class="pagination-info" id="users-pagination-info"></div>
                <div class="pagination-controls">
 <button type="button" class="btn btn-sm" id="users-prev-page" title="Previous Page">← Previous</button>
                    <span class="page-numbers" id="users-page-numbers"></span>
 <button type="button" class="btn btn-sm" id="users-next-page" title="Next Page">Next →</button>
                </div>
            </div>
        </section>

        <!-- Content Management Section -->
        <section class="admin-section" id="content-section">
            <div class="section-header">
                <h1>Content Management</h1>
 <div class="header-actions" role="group" aria-label="Content Management Actions">
                    <button type="button" class="btn btn-primary" id="create-article">📝 New Article</button>
                    <button type="button" class="btn btn-secondary" id="manage-games">🎮 Manage Games</button>
                </div>
            </div>

            <!-- Content Tabs -->
            <div class="content-tabs">
                <button type="button" class="tab-btn active" data-tab="articles">📰 Articles</button>
                <button type="button" class="tab-btn" data-tab="games">🎮 Games</button>
                <button type="button" class="tab-btn" data-tab="announcements">📢 Announcements</button>
            </div>

            <!-- Articles Tab -->
            <div class="tab-content active" id="articles-tab">
                <div class="content-filters">
                    <select id="article-status-filter">
                        <option value="">All Articles</option>
                        <option value="published">Published</option>
                        <option value="draft">Draft</option>
                        <option value="archived">Archived</option>
                    </select>
                    <select id="article-category-filter">
                        <option value="">All Categories</option>
                        <option value="updates">Updates</option>
                        <option value="games">Games</option>
                        <option value="community">Community</option>
                        <option value="events">Events</option>
                    </select>
                </div>
                <div class="articles-grid" id="articles-grid">
                    <!-- Articles will be loaded here -->
                </div>
            </div>

            <!-- Games Tab -->
            <div class="tab-content" id="games-tab">
                <div class="games-management" id="games-management">
                    <!-- Games management content -->
                </div>
            </div>

            <!-- Announcements Tab -->
            <div class="tab-content" id="announcements-tab">
                <div class="announcements-list" id="announcements-list">
                    <!-- Announcements content -->
                </div>
            </div>
        </section>

        <!-- Tournament Management Section -->
        <section class="admin-section" id="tournaments-section">
            <div class="section-header">
                <h1>Tournament Management</h1>
 <div class="header-actions" role="group" aria-label="Tournament Management Actions">
                    <button type="button" class="btn btn-primary" id="create-tournament">🏆 Create Tournament</button>
                    <button type="button" class="btn btn-secondary" id="tournament-templates">📋 Templates</button>
                </div>
            </div>

            <!-- Tournament Status Cards -->
            <div class="tournament-status-grid">
                <div class="status-card">
                    <div class="status-icon">🏃</div>
                    <div class="status-content">
                        <div class="status-value" id="active-tournaments-count">0</div>
                        <div class="status-label">Active Tournaments</div>
                    </div>
                </div>
                <div class="status-card">
                    <div class="status-icon">⏳</div>
                    <div class="status-content">
                        <div class="status-value" id="upcoming-tournaments-count">0</div>
                        <div class="status-label">Upcoming</div>
                    </div>
                </div>
                <div class="status-card">
                    <div class="status-icon">👥</div>
                    <div class="status-content">
                        <div class="status-value" id="total-participants">0</div>
                        <div class="status-label">Total Participants</div>
                    </div>
                </div>
                <div class="status-card">
                    <div class="status-icon">💰</div>
                    <div class="status-content">
                        <div class="status-value" id="total-prize-pool">$0</div>
                        <div class="status-label">Total Prize Pool</div>
                    </div>
                </div>
            </div>

            <!-- Tournaments List -->
            <div class="tournaments-container">
                <div class="tournaments-filters">
                    <select id="tournament-status-filter">
                        <option value="">All Tournaments</option>
                        <option value="active">Active</option>
                        <option value="upcoming">Upcoming</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    <select id="tournament-game-filter">
                        <option value="">All Games</option>
                        <option value="snake">Snake</option>
                        <option value="tetris">Tetris</option>
                        <option value="pong">Pong</option>
                    </select>
                </div>
                <div class="tournaments-grid" id="tournaments-grid">
                    <!-- Tournaments will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Moderation Section -->
        <section class="admin-section" id="moderation-section">
            <div class="section-header">
                <h1>Moderation Tools</h1>
 <div class="header-actions" role="group" aria-label="Moderation Actions">
                    <button type="button" class="btn btn-danger" id="emergency-mode">🚨 Emergency Mode</button>
                    <button type="button" class="btn btn-secondary" id="moderation-settings">⚙️ Settings</button>
                </div>
            </div>

            <!-- Moderation Stats -->
            <div class="moderation-stats">
                <div class="mod-stat-card">
                    <div class="mod-stat-icon">🚨</div>
                    <div class="mod-stat-content">
                        <div class="mod-stat-value" id="pending-reports">0</div>
                        <div class="mod-stat-label">Pending Reports</div>
                    </div>
                </div>
                <div class="mod-stat-card">
                    <div class="mod-stat-icon">⚠️</div>
                    <div class="mod-stat-content">
                        <div class="mod-stat-value" id="flagged-content">0</div>
                        <div class="mod-stat-label">Flagged Content</div>
                    </div>
                </div>
                <div class="mod-stat-card">
                    <div class="mod-stat-icon">🔨</div>
                    <div class="mod-stat-content">
                        <div class="mod-stat-value" id="actions-today">0</div>
                        <div class="mod-stat-label">Actions Today</div>
                    </div>
                </div>
            </div>

            <!-- Moderation Queue -->
            <div class="moderation-queue">
                <div class="queue-header">
                    <h3>Moderation Queue</h3>
                    <div class="queue-filters">
                        <select id="report-type-filter">
                            <option value="">All Reports</option>
                            <option value="spam">Spam</option>
                            <option value="harassment">Harassment</option>
                            <option value="cheating">Cheating</option>
                            <option value="inappropriate">Inappropriate Content</option>
                        </select>
                        <select id="report-priority-filter">
                            <option value="">All Priorities</option>
                            <option value="high">High Priority</option>
                            <option value="medium">Medium Priority</option>
                            <option value="low">Low Priority</option>
                        </select>
                    </div>
                </div>
                <div class="reports-list" id="reports-list">
                    <!-- Reports will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section class="admin-section" id="settings-section">
            <div class="section-header">
                <h1>System Settings</h1>
 <div class="header-actions" role="group" aria-label="System Settings Actions">
                    <button type="button" class="btn btn-primary" id="save-settings">💾 Save Changes</button>
                    <button type="button" class="btn btn-secondary" id="reset-settings">🔄 Reset</button>
                </div>
            </div>

            <!-- Settings Content -->
            <div class="settings-grid">
                <div class="settings-card">
                    <h3>General Settings</h3>
                    <div class="settings-form" id="general-settings">
                        <!-- General settings form -->
                    </div>
                </div>
                <div class="settings-card">
                    <h3>Game Settings</h3>
                    <div class="settings-form" id="game-settings">
                        <!-- Game settings form -->
                    </div>
                </div>
                <div class="settings-card">
                    <h3>Security Settings</h3>
                    <div class="settings-form" id="security-settings">
                        <!-- Security settings form -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Logs Section -->
        <section class="admin-section" id="logs-section">
            <div class="section-header">
                <h1>System Logs</h1>
 <div class="header-actions" role="group" aria-label="System Logs Actions">
                    <button type="button" class="btn btn-primary" id="export-logs">📊 Export Logs</button>
                    <button type="button" class="btn btn-secondary" id="clear-logs">🗑️ Clear Logs</button>
                </div>
            </div>

            <!-- Logs Content -->
            <div class="logs-container">
                <div class="logs-filters">
                    <select id="log-level-filter">
                        <option value="">All Levels</option>
                        <option value="error">Error</option>
                        <option value="warning">Warning</option>
                        <option value="info">Info</option>
                        <option value="debug">Debug</option>
                    </select>
                    <select id="log-category-filter">
                        <option value="">All Categories</option>
                        <option value="auth">Authentication</option>
                        <option value="game">Game Events</option>
                        <option value="user">User Actions</option>
                        <option value="system">System</option>
                    </select>
                    <input type="date" id="log-date-filter" class="date-filter">
                </div>
                <div class="logs-list" id="logs-list">
                    <!-- Logs will be loaded here -->
                </div>
            </div>
        </section>
    </main>

    <!-- Modals and Overlays -->
    <div class="modal-overlay" id="modal-overlay"></div>

    <!-- Scripts -->
    <script src="js/supabase.js"></script>
    <script src="js/data.js"></script>
    <script src="js/theme.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
