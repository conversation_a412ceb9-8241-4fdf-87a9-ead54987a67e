<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Browse and play our collection of browser-based 2D games. Snake, Tetris, Pong and more classic arcade games.">
    <meta name="keywords" content="browser games, 2D games, arcade games, snake game, tetris game, pong game">
    <meta name="author" content="GameHub Team">
    <title>Games Library - GameHub | Play Free 2D Games Online</title>
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/pages.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <span class="nav-logo">🎮</span>
                <span class="nav-title">GameHub</span>
            </div>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="games.html" class="nav-link active">Games</a>
                </li>
                <li class="nav-item">
                    <a href="leaderboards.html" class="nav-link">Leaderboards</a>
                </li>
                <li class="nav-item">
                    <a href="news.html" class="nav-link">News</a>
                </li>
                <li class="nav-item nav-dropdown">
                    <a href="#" class="nav-link" id="more-dropdown">More ▼</a>
                    <ul class="dropdown-menu" id="dropdown-menu">
                        <li><a href="about.html" class="dropdown-link">About</a></li>
                        <li><a href="contact.html" class="dropdown-link">Contact</a></li>
                        <li><a href="help.html" class="dropdown-link">Help</a></li>
                        <li><a href="profile.html" class="dropdown-link">Profile</a></li>
                        <li><a href="settings.html" class="dropdown-link">Settings</a></li>
                    </ul>
                </li>
            </ul>
            <div class="nav-actions">
                <button type="button" class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                    <span class="theme-icon">🌙</span>
                </button>
                <div class="social-links">
                    <a href="https://discord.gg/gamehub" target="_blank" rel="noopener" aria-label="Join Discord">
                        <span class="social-icon">💬</span>
                    </a>
                    <a href="https://github.com/gamehub" target="_blank" rel="noopener" aria-label="GitHub">
                        <span class="social-icon">⚡</span>
                    </a>
                </div>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <nav class="breadcrumb" aria-label="Breadcrumb">
        <div class="container">
            <ol class="breadcrumb-list">
                <li><a href="index.html">Home</a></li>
                <li aria-current="page">Games</li>
            </ol>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <div class="page-header-content">
                    <h1 class="page-title">
                        <span class="title-icon">🎮</span>
                        Game Library
                    </h1>
                    <p class="page-subtitle">Discover and play from our collection of amazing browser games</p>
                    <div class="page-stats">
                        <span class="stat"><strong id="games-count">8</strong> Games Available</span>
                        <span class="stat"><strong id="total-plays">150k+</strong> Total Plays</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Filters Section -->
        <section class="filters-section">
            <div class="container">
                <div class="filters-card">
                    <div class="filters-header">
                        <h3>Find Your Perfect Game</h3>
                        <button type="button" class="filters-reset" id="reset-filters">Reset Filters</button>
                    </div>
                    <div class="filters-content">
                        <div class="search-box">
                            <div class="search-input-wrapper">
                                <span class="search-icon">🔍</span>
                                <input type="text" id="search-input" placeholder="Search games..." class="search-input" aria-label="Search games">
                            </div>
                        </div>
                        <div class="filter-controls">
                            <div class="filter-group">
                                <label for="category-filter">Category</label>
                                <select id="category-filter" class="filter-select" aria-label="Filter by category">
                                    <option value="all">All Categories</option>
                                    <option value="arcade">Arcade</option>
                                    <option value="puzzle">Puzzle</option>
                                    <option value="action">Action</option>
                                    <option value="strategy">Strategy</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="difficulty-filter">Difficulty</label>
                                <select id="difficulty-filter" class="filter-select" aria-label="Filter by difficulty">
                                    <option value="all">All Levels</option>
                                    <option value="easy">Easy</option>
                                    <option value="medium">Medium</option>
                                    <option value="hard">Hard</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="sort-filter">Sort By</label>
                                <select id="sort-filter" class="filter-select" aria-label="Sort games">
                                    <option value="name">Name (A-Z)</option>
                                    <option value="rating">Highest Rated</option>
                                    <option value="plays">Most Popular</option>
                                    <option value="newest">Newest First</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Games Grid Section -->
        <section class="games-section">
            <div class="container">
                <div class="games-header">
                    <div class="games-count-display">
                        <span id="filtered-count">8</span> games found
                    </div>
                    <div class="view-toggle">
                        <button type="button" class="view-btn active" data-view="grid" aria-label="Grid view">
                            <span class="view-icon">⊞</span>
                        </button>
                        <button type="button" class="view-btn" data-view="list" aria-label="List view">
                            <span class="view-icon">☰</span>
                        </button>
                    </div>
                </div>

                <!-- Loading State -->
                <div class="loading-state" id="loading-state">
                    <div class="loading-spinner"></div>
                    <p>Loading games...</p>
                </div>

                <!-- Games Grid -->
                <div class="games-grid" id="games-grid">
                    <!-- Games will be loaded here by JavaScript -->
                </div>

                <!-- Empty State -->
                <div class="empty-state hidden" id="empty-state">
                    <div class="empty-icon">🎮</div>
                    <h3>No games found</h3>
                    <p>Try adjusting your search or filter criteria</p>
                    <button type="button" class="btn btn-primary" onclick="resetFilters()">Reset Filters</button>
                </div>
            </div>
        </section>

        <!-- Featured Categories -->
        <section class="section bg-dark">
            <div class="container">
                <h2 class="section-title">Browse by Category</h2>
                <div class="category-grid">
                    <div class="category-card" data-category="arcade">
                        <div class="category-icon">🕹️</div>
                        <h3>Arcade</h3>
                        <p>Classic arcade games with fast-paced action</p>
                        <span class="category-count">4 games</span>
                    </div>
                    <div class="category-card" data-category="puzzle">
                        <div class="category-icon">🧩</div>
                        <h3>Puzzle</h3>
                        <p>Brain-teasing puzzles and logic games</p>
                        <span class="category-count">2 games</span>
                    </div>
                    <div class="category-card" data-category="action">
                        <div class="category-icon">⚡</div>
                        <h3>Action</h3>
                        <p>Fast-paced games requiring quick reflexes</p>
                        <span class="category-count">2 games</span>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Game Detail Modal -->
    <div id="game-modal" class="modal">
        <div class="modal-content">
            <button type="button" class="modal-close" onclick="closeGameModal()" aria-label="Close modal">&times;</button>
            <div id="game-detail-content">
                <!-- Game details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Game Play Modal -->
    <div id="game-play-modal" class="modal">
        <div class="modal-content game-play-content">
            <div class="game-header">
                <h3 id="game-play-title">Game Title</h3>
                <div class="game-controls">
                    <button type="button" id="pause-btn" class="control-btn" aria-label="Pause game">⏸️</button>
                    <button type="button" id="restart-btn" class="control-btn" aria-label="Restart game">🔄</button>
                    <button type="button" class="control-btn" onclick="closeGamePlayModal()" aria-label="Close game">❌</button>
                </div>
            </div>
            <div class="game-container">
                <canvas id="game-canvas" width="800" height="600"></canvas>
                <div id="game-ui">
                    <div class="score">Score: <span id="score">0</span></div>
                    <div class="level">Level: <span id="level">1</span></div>
                </div>
            </div>
            <div class="game-instructions" id="game-instructions">
                <!-- Game instructions will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>GameHub</h3>
                    <p>Your ultimate destination for browser-based 2D games.</p>
                    <div class="social-links-footer">
                        <a href="https://discord.gg/gamehub" target="_blank" rel="noopener" aria-label="Discord">💬</a>
                        <a href="https://twitter.com/gamehub" target="_blank" rel="noopener" aria-label="Twitter">🐦</a>
                        <a href="https://github.com/gamehub" target="_blank" rel="noopener" aria-label="GitHub">⚡</a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="games.html">Games</a></li>
                        <li><a href="leaderboards.html">Leaderboards</a></li>
                        <li><a href="news.html">News</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="help.html">Help Center</a></li>
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="settings.html">Settings</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 GameHub. All rights reserved. Built with ❤️ for gamers everywhere.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/supabase.js"></script>
    <script src="js/data.js"></script>
    <script src="js/games/snake.js"></script>
    <script src="js/games/tetris.js"></script>
    <script src="js/games/pong.js"></script>
    <script src="js/theme.js"></script>
    <script src="js/games.js"></script>
</body>
</html>
