-- GameHub Supabase Database Schema
-- Run these commands in your Supabase SQL editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User profiles table
CREATE TABLE user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) UNIQUE NOT NULL,
    username TEXT UNIQUE,
    display_name TEXT,
    avatar_url TEXT,
    bio TEXT,
    games_played INTEGER DEFAULT 0,
    best_score INTEGER DEFAULT 0,
    time_played INTEGER DEFAULT 0, -- in minutes
    achievements_unlocked INTEGER DEFAULT 0,
    level INTEGER DEFAULT 1,
    experience_points INTEGER DEFAULT 0,
    preferred_theme TEXT DEFAULT 'dark',
    sound_enabled BOOLEAN DEFAULT true,
    notifications_enabled BOOLEAN DEFAULT true,
    privacy_level TEXT DEFAULT 'public', -- public, friends, private
    country TEXT,
    timezone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Game scores table
CREATE TABLE game_scores (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    game_id TEXT NOT NULL,
    score INTEGER NOT NULL,
    level_reached INTEGER,
    time_played INTEGER, -- in seconds
    moves_made INTEGER,
    metadata JSONB, -- game-specific data
    difficulty TEXT DEFAULT 'normal',
    is_personal_best BOOLEAN DEFAULT false,
    is_global_record BOOLEAN DEFAULT false,
    played_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Indexes for performance
    INDEX idx_game_scores_user_id (user_id),
    INDEX idx_game_scores_game_id (game_id),
    INDEX idx_game_scores_score (score DESC),
    INDEX idx_game_scores_played_at (played_at DESC)
);

-- Achievements table
CREATE TABLE achievements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    achievement_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    icon TEXT NOT NULL,
    category TEXT NOT NULL, -- gameplay, social, progression, special
    difficulty TEXT DEFAULT 'normal', -- easy, normal, hard, legendary
    points INTEGER DEFAULT 10,
    unlock_criteria JSONB NOT NULL, -- conditions to unlock
    is_hidden BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User achievements table (many-to-many)
CREATE TABLE user_achievements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    achievement_id TEXT REFERENCES achievements(achievement_id) NOT NULL,
    progress INTEGER DEFAULT 0,
    target INTEGER NOT NULL,
    is_unlocked BOOLEAN DEFAULT false,
    unlocked_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id, achievement_id),
    INDEX idx_user_achievements_user_id (user_id),
    INDEX idx_user_achievements_unlocked (is_unlocked, unlocked_at DESC)
);

-- News articles table
CREATE TABLE news_articles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    excerpt TEXT,
    content TEXT NOT NULL,
    featured_image_url TEXT,
    category TEXT NOT NULL, -- updates, games, community, events, tips
    tags TEXT[],
    author_id UUID REFERENCES auth.users(id),
    author_name TEXT NOT NULL,
    is_featured BOOLEAN DEFAULT false,
    is_published BOOLEAN DEFAULT false,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    INDEX idx_news_published (is_published, published_at DESC),
    INDEX idx_news_category (category),
    INDEX idx_news_featured (is_featured, published_at DESC)
);

-- Game statistics table
CREATE TABLE game_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    game_id TEXT NOT NULL,
    total_plays INTEGER DEFAULT 0,
    total_players INTEGER DEFAULT 0,
    average_score DECIMAL(10,2) DEFAULT 0,
    highest_score INTEGER DEFAULT 0,
    total_time_played INTEGER DEFAULT 0, -- in minutes
    difficulty_stats JSONB, -- stats per difficulty level
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(game_id)
);

-- User sessions table (for analytics)
CREATE TABLE user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    session_id TEXT NOT NULL,
    ip_address INET,
    user_agent TEXT,
    country TEXT,
    city TEXT,
    device_type TEXT, -- desktop, mobile, tablet
    browser TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- in seconds
    pages_visited INTEGER DEFAULT 0,
    games_played INTEGER DEFAULT 0,

    INDEX idx_user_sessions_user_id (user_id),
    INDEX idx_user_sessions_started_at (started_at DESC)
);

-- Tournaments table
CREATE TABLE tournaments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    game_id TEXT NOT NULL,
    tournament_type TEXT DEFAULT 'weekly', -- daily, weekly, monthly, special
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    max_participants INTEGER,
    entry_fee INTEGER DEFAULT 0,
    prize_pool JSONB, -- prize distribution
    rules JSONB,
    is_active BOOLEAN DEFAULT true,
    participant_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    INDEX idx_tournaments_active (is_active, start_date, end_date),
    INDEX idx_tournaments_game_id (game_id)
);

-- Tournament participants table
CREATE TABLE tournament_participants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    tournament_id UUID REFERENCES tournaments(id) NOT NULL,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    best_score INTEGER DEFAULT 0,
    rank INTEGER,
    prize_won JSONB,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(tournament_id, user_id),
    INDEX idx_tournament_participants_tournament (tournament_id, best_score DESC),
    INDEX idx_tournament_participants_user (user_id)
);

-- User friends table (social features)
CREATE TABLE user_friends (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    friend_id UUID REFERENCES auth.users(id) NOT NULL,
    status TEXT DEFAULT 'pending', -- pending, accepted, blocked
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,

    UNIQUE(user_id, friend_id),
    INDEX idx_user_friends_user_id (user_id, status),
    INDEX idx_user_friends_friend_id (friend_id, status)
);

-- Game reviews table
CREATE TABLE game_reviews (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    game_id TEXT NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_recommended BOOLEAN DEFAULT true,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id, game_id),
    INDEX idx_game_reviews_game_id (game_id, rating DESC),
    INDEX idx_game_reviews_user_id (user_id)
);

-- Notifications table
CREATE TABLE notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    type TEXT NOT NULL, -- achievement, friend_request, tournament, news, system
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB, -- additional notification data
    is_read BOOLEAN DEFAULT false,
    is_sent BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,

    INDEX idx_notifications_user_id (user_id, is_read, created_at DESC),
    INDEX idx_notifications_type (type, created_at DESC)
);

-- Create functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_news_articles_updated_at
    BEFORE UPDATE ON news_articles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_game_reviews_updated_at
    BEFORE UPDATE ON game_reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update user last_active
CREATE OR REPLACE FUNCTION update_user_last_active()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE user_profiles
    SET last_active = NOW()
    WHERE user_id = NEW.user_id;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to update last_active on game score insert
CREATE TRIGGER update_last_active_on_game_score
    AFTER INSERT ON game_scores
    FOR EACH ROW EXECUTE FUNCTION update_user_last_active();

-- Function to update game statistics
CREATE OR REPLACE FUNCTION update_game_statistics()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO game_statistics (game_id, total_plays, total_players, average_score, highest_score)
    VALUES (NEW.game_id, 1, 1, NEW.score, NEW.score)
    ON CONFLICT (game_id) DO UPDATE SET
        total_plays = game_statistics.total_plays + 1,
        total_players = (
            SELECT COUNT(DISTINCT user_id)
            FROM game_scores
            WHERE game_id = NEW.game_id
        ),
        average_score = (
            SELECT AVG(score)
            FROM game_scores
            WHERE game_id = NEW.game_id
        ),
        highest_score = GREATEST(game_statistics.highest_score, NEW.score),
        updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to update game statistics on new score
CREATE TRIGGER update_game_stats_on_score
    AFTER INSERT ON game_scores
    FOR EACH ROW EXECUTE FUNCTION update_game_statistics();

-- Enable Row Level Security (RLS)
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE news_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_statistics ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE tournaments ENABLE ROW LEVEL SECURITY;
ALTER TABLE tournament_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_friends ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_profiles
CREATE POLICY "Users can view public profiles" ON user_profiles
    FOR SELECT USING (
        privacy_level = 'public' OR
        auth.uid() = user_id OR
        (privacy_level = 'friends' AND EXISTS (
            SELECT 1 FROM user_friends
            WHERE user_id = user_profiles.user_id
            AND friend_id = auth.uid()
            AND status = 'accepted'
        ))
    );

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for game_scores
CREATE POLICY "Users can view own scores" ON game_scores
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own scores" ON game_scores
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Anyone can view leaderboards" ON game_scores
    FOR SELECT USING (true);

-- RLS Policies for user_achievements
CREATE POLICY "Users can view own achievements" ON user_achievements
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own achievements" ON user_achievements
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own achievements" ON user_achievements
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for news_articles
CREATE POLICY "Anyone can view published articles" ON news_articles
    FOR SELECT USING (is_published = true);

CREATE POLICY "Authors can manage own articles" ON news_articles
    FOR ALL USING (auth.uid() = author_id);

-- RLS Policies for game_statistics
CREATE POLICY "Anyone can view game statistics" ON game_statistics
    FOR SELECT USING (true);

-- RLS Policies for user_sessions
CREATE POLICY "Users can view own sessions" ON user_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sessions" ON user_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for tournaments
CREATE POLICY "Anyone can view active tournaments" ON tournaments
    FOR SELECT USING (is_active = true);

-- RLS Policies for tournament_participants
CREATE POLICY "Users can view tournament participants" ON tournament_participants
    FOR SELECT USING (true);

CREATE POLICY "Users can manage own participation" ON tournament_participants
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for user_friends
CREATE POLICY "Users can view own friends" ON user_friends
    FOR SELECT USING (auth.uid() = user_id OR auth.uid() = friend_id);

CREATE POLICY "Users can manage own friendships" ON user_friends
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for game_reviews
CREATE POLICY "Anyone can view reviews" ON game_reviews
    FOR SELECT USING (true);

CREATE POLICY "Users can manage own reviews" ON game_reviews
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for notifications
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Insert default achievements
INSERT INTO achievements (achievement_id, name, description, icon, category, points, unlock_criteria) VALUES
('first_victory', 'First Victory', 'Win your first game', '🏆', 'gameplay', 10, '{"type": "games_won", "target": 1}'),
('hot_streak', 'Hot Streak', 'Win 5 games in a row', '🔥', 'gameplay', 25, '{"type": "win_streak", "target": 5}'),
('high_scorer', 'High Scorer', 'Reach 10,000 points in any game', '⭐', 'gameplay', 20, '{"type": "high_score", "target": 10000}'),
('perfectionist', 'Perfectionist', 'Complete a perfect game', '🎯', 'gameplay', 30, '{"type": "perfect_game", "target": 1}'),
('dedicated_player', 'Dedicated Player', 'Play for 10 hours total', '⏰', 'progression', 15, '{"type": "time_played", "target": 600}'),
('game_master', 'Game Master', 'Play all available games', '🌟', 'progression', 50, '{"type": "games_played", "target": 8}'),
('social_butterfly', 'Social Butterfly', 'Add 10 friends', '🦋', 'social', 20, '{"type": "friends_added", "target": 10}'),
('tournament_winner', 'Tournament Winner', 'Win a tournament', '🏅', 'special', 100, '{"type": "tournament_wins", "target": 1}'),
('speed_demon', 'Speed Demon', 'Complete a game in under 30 seconds', '⚡', 'gameplay', 25, '{"type": "speed_completion", "target": 30}'),
('marathon_runner', 'Marathon Runner', 'Play for 2 hours straight', '🏃', 'progression', 35, '{"type": "session_duration", "target": 120}');

-- Insert sample news articles
INSERT INTO news_articles (title, slug, excerpt, content, category, author_name, is_published, published_at) VALUES
('GameHub 2.0 Launch', 'gamehub-2-0-launch', 'Major platform update with new features and improvements', 'We are excited to announce the launch of GameHub 2.0...', 'updates', 'GameHub Team', true, NOW()),
('New Leaderboard System', 'new-leaderboard-system', 'Enhanced competitive features and global rankings', 'Our new leaderboard system brings enhanced competitive features...', 'updates', 'GameHub Team', true, NOW() - INTERVAL '5 days'),
('Community Tournament Results', 'community-tournament-results', 'Congratulations to all participants in our first tournament', 'The first GameHub community tournament has concluded...', 'community', 'GameHub Team', true, NOW() - INTERVAL '10 days');

-- Create indexes for better performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_profiles_username ON user_profiles(username);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_profiles_last_active ON user_profiles(last_active DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_game_scores_composite ON game_scores(game_id, score DESC, played_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_achievements_progress ON user_achievements(user_id, is_unlocked, progress);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_news_articles_slug ON news_articles(slug);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tournaments_dates ON tournaments(start_date, end_date, is_active);

-- Create views for common queries
CREATE VIEW leaderboard_view AS
SELECT
    gs.game_id,
    up.username,
    up.display_name,
    up.avatar_url,
    gs.score,
    gs.played_at,
    ROW_NUMBER() OVER (PARTITION BY gs.game_id ORDER BY gs.score DESC) as rank
FROM game_scores gs
JOIN user_profiles up ON gs.user_id = up.user_id
WHERE up.privacy_level = 'public';

CREATE VIEW user_stats_view AS
SELECT
    up.user_id,
    up.username,
    up.display_name,
    up.games_played,
    up.best_score,
    up.time_played,
    up.achievements_unlocked,
    COUNT(ua.id) FILTER (WHERE ua.is_unlocked = true) as total_achievements,
    AVG(gs.score) as average_score,
    MAX(gs.score) as highest_score
FROM user_profiles up
LEFT JOIN user_achievements ua ON up.user_id = ua.user_id
LEFT JOIN game_scores gs ON up.user_id = gs.user_id
GROUP BY up.user_id, up.username, up.display_name, up.games_played, up.best_score, up.time_played, up.achievements_unlocked;
