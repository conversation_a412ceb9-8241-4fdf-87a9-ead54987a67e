<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Customize your GameHub experience with personalized settings and preferences.">
    <meta name="keywords" content="settings, preferences, dark mode, game settings, user preferences">
    <meta name="author" content="GameHub Team">
    <title>Settings - GameHub | Customize Your Gaming Experience</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <span class="nav-logo">🎮</span>
                <span class="nav-title">GameHub</span>
            </div>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="games.html" class="nav-link">Games</a>
                </li>
                <li class="nav-item">
                    <a href="leaderboards.html" class="nav-link">Leaderboards</a>
                </li>
                <li class="nav-item">
                    <a href="news.html" class="nav-link">News</a>
                </li>
                <li class="nav-item nav-dropdown">
                    <a href="#" class="nav-link active" id="more-dropdown">More ▼</a>
                    <ul class="dropdown-menu" id="dropdown-menu">
                        <li><a href="about.html" class="dropdown-link">About</a></li>
                        <li><a href="contact.html" class="dropdown-link">Contact</a></li>
                        <li><a href="help.html" class="dropdown-link">Help</a></li>
                        <li><a href="profile.html" class="dropdown-link">Profile</a></li>
                        <li><a href="settings.html" class="dropdown-link active">Settings</a></li>
                    </ul>
                </li>
            </ul>
            <div class="nav-actions">
                <button type="button" class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                    <span class="theme-icon">🌙</span>
                </button>
                <div class="social-links">
                    <a href="https://discord.gg/gamehub" target="_blank" rel="noopener" aria-label="Join Discord">
                        <span class="social-icon">💬</span>
                    </a>
                    <a href="https://github.com/gamehub" target="_blank" rel="noopener" aria-label="GitHub">
                        <span class="social-icon">⚡</span>
                    </a>
                </div>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <nav class="breadcrumb" aria-label="Breadcrumb">
        <div class="container">
            <ol class="breadcrumb-list">
                <li><a href="index.html">Home</a></li>
                <li aria-current="page">Settings</li>
            </ol>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <div class="page-header-content">
                    <h1 class="page-title">
                        <span class="title-icon">⚙️</span>
                        Settings
                    </h1>
                    <p class="page-subtitle">Customize your GameHub experience</p>
                </div>
            </div>
        </section>

        <!-- Settings Content -->
        <section class="settings-section">
            <div class="container">
                <div class="settings-layout">
                    <!-- Settings Sidebar -->
                    <div class="settings-sidebar">
                        <nav class="settings-nav">
                            <button type="button" class="settings-nav-item active" data-section="appearance">
                                <span class="nav-icon">🎨</span>
                                <span class="nav-text">Appearance</span>
                            </button>
                            <button type="button" class="settings-nav-item" data-section="audio">
                                <span class="nav-icon">🔊</span>
                                <span class="nav-text">Audio</span>
                            </button>
                            <button type="button" class="settings-nav-item" data-section="gameplay">
                                <span class="nav-icon">🎮</span>
                                <span class="nav-text">Gameplay</span>
                            </button>
                            <button type="button" class="settings-nav-item" data-section="controls">
                                <span class="nav-icon">⌨️</span>
                                <span class="nav-text">Controls</span>
                            </button>
                            <button type="button" class="settings-nav-item" data-section="privacy">
                                <span class="nav-icon">🔒</span>
                                <span class="nav-text">Privacy</span>
                            </button>
                            <button type="button" class="settings-nav-item" data-section="account">
                                <span class="nav-icon">👤</span>
                                <span class="nav-text">Account</span>
                            </button>
                        </nav>
                    </div>

                    <!-- Settings Content -->
                    <div class="settings-content">
                        <!-- Appearance Settings -->
                        <div class="settings-panel active" id="appearance-panel">
                            <div class="panel-header">
                                <h2>Appearance</h2>
                                <p>Customize the look and feel of GameHub</p>
                            </div>
                            <div class="settings-group">
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Theme</h3>
                                        <p>Choose your preferred color scheme</p>
                                    </div>
                                    <div class="setting-control">
                                        <div class="theme-selector">
                                            <label class="theme-option">
                                                <input type="radio" name="theme" value="light">
                                                <span class="theme-preview light">
                                                    <span class="theme-name">Light</span>
                                                </span>
                                            </label>
                                            <label class="theme-option">
                                                <input type="radio" name="theme" value="dark" checked>
                                                <span class="theme-preview dark">
                                                    <span class="theme-name">Dark</span>
                                                </span>
                                            </label>
                                            <label class="theme-option">
                                                <input type="radio" name="theme" value="auto">
                                                <span class="theme-preview auto">
                                                    <span class="theme-name">Auto</span>
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Animations</h3>
                                        <p>Enable or disable interface animations</p>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="animations-toggle" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Reduced Motion</h3>
                                        <p>Minimize motion for accessibility</p>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="reduced-motion-toggle">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Audio Settings -->
                        <div class="settings-panel" id="audio-panel">
                            <div class="panel-header">
                                <h2>Audio</h2>
                                <p>Control sound effects and music</p>
                            </div>
                            <div class="settings-group">
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Master Volume</h3>
                                        <p>Overall volume level</p>
                                    </div>
                                    <div class="setting-control">
                                        <div class="volume-control">
                                            <input type="range" id="master-volume" min="0" max="100" value="80" class="volume-slider">
                                            <span class="volume-value">80%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Sound Effects</h3>
                                        <p>Game sound effects volume</p>
                                    </div>
                                    <div class="setting-control">
                                        <div class="volume-control">
                                            <input type="range" id="sfx-volume" min="0" max="100" value="70" class="volume-slider">
                                            <span class="volume-value">70%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Background Music</h3>
                                        <p>Menu and game background music</p>
                                    </div>
                                    <div class="setting-control">
                                        <div class="volume-control">
                                            <input type="range" id="music-volume" min="0" max="100" value="50" class="volume-slider">
                                            <span class="volume-value">50%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Mute All</h3>
                                        <p>Disable all audio</p>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="mute-all-toggle">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Gameplay Settings -->
                        <div class="settings-panel" id="gameplay-panel">
                            <div class="panel-header">
                                <h2>Gameplay</h2>
                                <p>Adjust game difficulty and behavior</p>
                            </div>
                            <div class="settings-group">
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Default Difficulty</h3>
                                        <p>Starting difficulty for new games</p>
                                    </div>
                                    <div class="setting-control">
                                        <select id="default-difficulty" class="setting-select">
                                            <option value="easy">Easy</option>
                                            <option value="medium" selected>Medium</option>
                                            <option value="hard">Hard</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Auto-Save Progress</h3>
                                        <p>Automatically save game progress</p>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="auto-save-toggle" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Show Hints</h3>
                                        <p>Display helpful hints during gameplay</p>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="show-hints-toggle" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Pause on Focus Loss</h3>
                                        <p>Automatically pause when switching tabs</p>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="pause-focus-toggle" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Controls Settings -->
                        <div class="settings-panel" id="controls-panel">
                            <div class="panel-header">
                                <h2>Controls</h2>
                                <p>Customize keyboard controls for games</p>
                            </div>
                            <div class="settings-group">
                                <div class="controls-info">
                                    <p>Click on a control to change its key binding</p>
                                </div>
                                <div class="controls-grid">
                                    <div class="control-item">
                                        <span class="control-label">Move Up</span>
                                        <button type="button" class="control-key" data-action="up">↑</button>
                                    </div>
                                    <div class="control-item">
                                        <span class="control-label">Move Down</span>
                                        <button type="button" class="control-key" data-action="down">↓</button>
                                    </div>
                                    <div class="control-item">
                                        <span class="control-label">Move Left</span>
                                        <button type="button" class="control-key" data-action="left">←</button>
                                    </div>
                                    <div class="control-item">
                                        <span class="control-label">Move Right</span>
                                        <button type="button" class="control-key" data-action="right">→</button>
                                    </div>
                                    <div class="control-item">
                                        <span class="control-label">Action/Rotate</span>
                                        <button type="button" class="control-key" data-action="action">Space</button>
                                    </div>
                                    <div class="control-item">
                                        <span class="control-label">Pause</span>
                                        <button type="button" class="control-key" data-action="pause">P</button>
                                    </div>
                                </div>
                                <div class="controls-actions">
                                    <button type="button" class="btn btn-secondary" id="reset-controls">Reset to Default</button>
                                </div>
                            </div>
                        </div>

                        <!-- Privacy Settings -->
                        <div class="settings-panel" id="privacy-panel">
                            <div class="panel-header">
                                <h2>Privacy</h2>
                                <p>Control your data and privacy preferences</p>
                            </div>
                            <div class="settings-group">
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Analytics</h3>
                                        <p>Help improve GameHub by sharing usage data</p>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="analytics-toggle" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Save Game History</h3>
                                        <p>Store your game history locally</p>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="save-history-toggle" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h3>Clear Data</h3>
                                        <p>Remove all stored game data and preferences</p>
                                    </div>
                                    <div class="setting-control">
                                        <button type="button" class="btn btn-danger" id="clear-data-btn">Clear All Data</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Settings -->
                        <div class="settings-panel" id="account-panel">
                            <div class="panel-header">
                                <h2>Account</h2>
                                <p>Manage your GameHub account</p>
                            </div>
                            <div class="settings-group">
                                <div class="account-info">
                                    <div class="account-avatar">👤</div>
                                    <div class="account-details">
                                        <h3>Guest Player</h3>
                                        <p>Playing as a guest</p>
                                    </div>
                                </div>
                                <div class="account-actions">
                                    <button type="button" class="btn btn-primary">Create Account</button>
                                    <button type="button" class="btn btn-secondary">Sign In</button>
                                </div>
                                <div class="account-benefits">
                                    <h4>Benefits of creating an account:</h4>
                                    <ul>
                                        <li>Save your progress across devices</li>
                                        <li>Compete in global leaderboards</li>
                                        <li>Unlock achievements and badges</li>
                                        <li>Get personalized game recommendations</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Settings -->
                <div class="settings-actions">
                    <button type="button" class="btn btn-primary" id="save-settings">Save Changes</button>
                    <button type="button" class="btn btn-secondary" id="reset-settings">Reset to Defaults</button>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>GameHub</h3>
                    <p>Your ultimate destination for browser-based 2D games.</p>
                    <div class="social-links-footer">
                        <a href="https://discord.gg/gamehub" target="_blank" rel="noopener" aria-label="Discord">💬</a>
                        <a href="https://twitter.com/gamehub" target="_blank" rel="noopener" aria-label="Twitter">🐦</a>
                        <a href="https://github.com/gamehub" target="_blank" rel="noopener" aria-label="GitHub">⚡</a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="games.html">Games</a></li>
                        <li><a href="leaderboards.html">Leaderboards</a></li>
                        <li><a href="news.html">News</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="help.html">Help Center</a></li>
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="settings.html">Settings</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 GameHub. All rights reserved. Built with ❤️ for gamers everywhere.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/data.js"></script>
    <script src="js/theme.js"></script>
    <script src="js/settings.js"></script>
</body>
</html>
