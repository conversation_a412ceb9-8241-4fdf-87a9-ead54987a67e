// Supabase Configuration and Integration
class SupabaseClient {
    constructor() {
        // Replace these with your actual Supabase project credentials
        this.supabaseUrl = 'https://vogmlmjqifwepbygydsx.supabase.co';
        this.supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvZ21sbWpxaWZ3ZXBieWd5ZHN4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0NzcyNjEsImV4cCI6MjA2NDA1MzI2MX0.6aMvkHl1e729VG6cy_hUxPbN4uSzBanJNUKNemi1DMQ';
        this.client = null;
        this.user = null;
        this.session = null;
        this.init();
    }

    async init() {
        try {
            // Load Supabase client from CDN
            await this.loadSupabaseClient();

            // Initialize Supabase client
            this.client = window.supabase.createClient(this.supabaseUrl, this.supabaseKey);

            // Check for existing session
            await this.checkSession();

            // Listen for auth changes
            this.setupAuthListener();

            console.log('Supabase initialized successfully');
        } catch (error) {
            console.warn('Supabase initialization failed:', error);
            // Fallback to local storage for offline functionality
            this.initOfflineMode();
        }
    }

    async loadSupabaseClient() {
        return new Promise((resolve, reject) => {
            if (window.supabase) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    initOfflineMode() {
        console.log('Running in offline mode - using localStorage');
        this.isOffline = true;
    }

    async checkSession() {
        if (this.isOffline) return;

        try {
            const { data: { session }, error } = await this.client.auth.getSession();
            if (error) throw error;

            this.session = session;
            this.user = session?.user || null;

            if (this.user) {
                this.onAuthStateChange('SIGNED_IN', session);
            }
        } catch (error) {
            console.error('Error checking session:', error);
        }
    }

    setupAuthListener() {
        if (this.isOffline) return;

        this.client.auth.onAuthStateChange((event, session) => {
            this.session = session;
            this.user = session?.user || null;
            this.onAuthStateChange(event, session);
        });
    }

    onAuthStateChange(event, session) {
        console.log('Auth state changed:', event, session);

        // Update UI based on auth state
        this.updateAuthUI(event, session);

        // Sync local data with cloud if signed in
        if (event === 'SIGNED_IN' && session) {
            this.syncUserData();
        }
    }

    updateAuthUI(event, session) {
        const authButtons = document.querySelectorAll('.auth-button');
        const userInfo = document.querySelectorAll('.user-info');

        if (event === 'SIGNED_IN' && session) {
            // User is signed in
            authButtons.forEach(btn => {
                if (btn.textContent.includes('Sign In') || btn.textContent.includes('Create Account')) {
                    btn.style.display = 'none';
                }
            });

            userInfo.forEach(info => {
                info.style.display = 'block';
                const nameElement = info.querySelector('.user-name');
                if (nameElement) {
                    nameElement.textContent = session.user.email || 'User';
                }
            });
        } else {
            // User is signed out
            authButtons.forEach(btn => {
                if (btn.textContent.includes('Sign In') || btn.textContent.includes('Create Account')) {
                    btn.style.display = 'inline-flex';
                }
            });

            userInfo.forEach(info => {
                info.style.display = 'none';
            });
        }
    }

    // Authentication Methods
    async signUp(email, password, userData = {}) {
        if (this.isOffline) {
            throw new Error('Authentication requires internet connection');
        }

        try {
            const { data, error } = await this.client.auth.signUp({
                email,
                password,
                options: {
                    data: userData
                }
            });

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Sign up error:', error);
            throw error;
        }
    }

    async signIn(email, password) {
        if (this.isOffline) {
            throw new Error('Authentication requires internet connection');
        }

        try {
            const { data, error } = await this.client.auth.signInWithPassword({
                email,
                password
            });

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Sign in error:', error);
            throw error;
        }
    }

    async signOut() {
        if (this.isOffline) return;

        try {
            const { error } = await this.client.auth.signOut();
            if (error) throw error;
        } catch (error) {
            console.error('Sign out error:', error);
            throw error;
        }
    }

    // Data Management Methods
    async saveUserData(data) {
        if (this.isOffline || !this.user) {
            // Save to localStorage as fallback
            this.saveToLocalStorage('userData', data);
            return;
        }

        try {
            const { error } = await this.client
                .from('user_profiles')
                .upsert({
                    user_id: this.user.id,
                    ...data,
                    updated_at: new Date().toISOString()
                });

            if (error) throw error;

            // Also save to localStorage for offline access
            this.saveToLocalStorage('userData', data);
        } catch (error) {
            console.error('Error saving user data:', error);
            // Fallback to localStorage
            this.saveToLocalStorage('userData', data);
        }
    }

    async getUserData() {
        if (this.isOffline || !this.user) {
            return this.getFromLocalStorage('userData', {});
        }

        try {
            const { data, error } = await this.client
                .from('user_profiles')
                .select('*')
                .eq('user_id', this.user.id)
                .single();

            if (error && error.code !== 'PGRST116') throw error;

            const userData = data || {};

            // Merge with local data
            const localData = this.getFromLocalStorage('userData', {});
            const mergedData = { ...localData, ...userData };

            return mergedData;
        } catch (error) {
            console.error('Error fetching user data:', error);
            return this.getFromLocalStorage('userData', {});
        }
    }

    async saveGameScore(gameId, score, metadata = {}) {
        const scoreData = {
            game_id: gameId,
            score: score,
            metadata: metadata,
            played_at: new Date().toISOString()
        };

        if (this.isOffline || !this.user) {
            // Save to localStorage
            const scores = this.getFromLocalStorage('gameScores', []);
            scores.push(scoreData);
            this.saveToLocalStorage('gameScores', scores);
            return;
        }

        try {
            const { error } = await this.client
                .from('game_scores')
                .insert({
                    user_id: this.user.id,
                    ...scoreData
                });

            if (error) throw error;

            // Also save locally
            const scores = this.getFromLocalStorage('gameScores', []);
            scores.push(scoreData);
            this.saveToLocalStorage('gameScores', scores);
        } catch (error) {
            console.error('Error saving game score:', error);
            // Fallback to localStorage
            const scores = this.getFromLocalStorage('gameScores', []);
            scores.push(scoreData);
            this.saveToLocalStorage('gameScores', scores);
        }
    }

    async getGameScores(gameId = null) {
        const localScores = this.getFromLocalStorage('gameScores', []);

        if (this.isOffline || !this.user) {
            return gameId ? localScores.filter(s => s.game_id === gameId) : localScores;
        }

        try {
            let query = this.client
                .from('game_scores')
                .select('*')
                .eq('user_id', this.user.id)
                .order('played_at', { ascending: false });

            if (gameId) {
                query = query.eq('game_id', gameId);
            }

            const { data, error } = await query;
            if (error) throw error;

            // Merge with local scores
            const filteredLocal = gameId ? localScores.filter(s => s.game_id === gameId) : localScores;
            return [...(data || []), ...filteredLocal];
        } catch (error) {
            console.error('Error fetching game scores:', error);
            return gameId ? localScores.filter(s => s.game_id === gameId) : localScores;
        }
    }

    async getLeaderboard(gameId, limit = 10) {
        if (this.isOffline) {
            // Return mock leaderboard data
            return this.getMockLeaderboard(gameId, limit);
        }

        try {
            const { data, error } = await this.client
                .from('game_scores')
                .select(`
                    score,
                    played_at,
                    user_profiles (
                        username,
                        avatar_url
                    )
                `)
                .eq('game_id', gameId)
                .order('score', { ascending: false })
                .limit(limit);

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching leaderboard:', error);
            return this.getMockLeaderboard(gameId, limit);
        }
    }

    // Utility Methods
    saveToLocalStorage(key, data) {
        try {
            localStorage.setItem(`gamehub_${key}`, JSON.stringify(data));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    }

    getFromLocalStorage(key, defaultValue = null) {
        try {
            const data = localStorage.getItem(`gamehub_${key}`);
            return data ? JSON.parse(data) : defaultValue;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return defaultValue;
        }
    }

    async syncUserData() {
        if (this.isOffline || !this.user) return;

        try {
            // Sync local scores to cloud
            const localScores = this.getFromLocalStorage('gameScores', []);
            for (const score of localScores) {
                if (!score.synced) {
                    await this.saveGameScore(score.game_id, score.score, score.metadata);
                    score.synced = true;
                }
            }
            this.saveToLocalStorage('gameScores', localScores);

            // Sync user data
            const localUserData = this.getFromLocalStorage('userData', {});
            if (Object.keys(localUserData).length > 0) {
                await this.saveUserData(localUserData);
            }
        } catch (error) {
            console.error('Error syncing user data:', error);
        }
    }

    getMockLeaderboard(gameId, limit) {
        const mockData = [
            { score: 12450, user_profiles: { username: 'Champion', avatar_url: null }, played_at: '2024-01-15' },
            { score: 8750, user_profiles: { username: 'Player2', avatar_url: null }, played_at: '2024-01-14' },
            { score: 7320, user_profiles: { username: 'Player3', avatar_url: null }, played_at: '2024-01-13' },
            { score: 6890, user_profiles: { username: 'Player4', avatar_url: null }, played_at: '2024-01-12' },
            { score: 5670, user_profiles: { username: 'Player5', avatar_url: null }, played_at: '2024-01-11' }
        ];
        return mockData.slice(0, limit);
    }

    // Real-time Subscriptions
    subscribeToLeaderboard(gameId, callback) {
        if (this.isOffline || !this.client) {
            console.warn('Real-time subscriptions not available in offline mode');
            return null;
        }

        try {
            const subscription = this.client
                .channel(`leaderboard-${gameId}`)
                .on('postgres_changes', {
                    event: 'INSERT',
                    schema: 'public',
                    table: 'game_scores',
                    filter: `game_id=eq.${gameId}`
                }, (payload) => {
                    console.log('New score added:', payload);
                    callback(payload);
                })
                .subscribe();

            return subscription;
        } catch (error) {
            console.error('Error subscribing to leaderboard:', error);
            return null;
        }
    }

    subscribeToUserAchievements(userId, callback) {
        if (this.isOffline || !this.client) {
            console.warn('Real-time subscriptions not available in offline mode');
            return null;
        }

        try {
            const subscription = this.client
                .channel(`achievements-${userId}`)
                .on('postgres_changes', {
                    event: 'UPDATE',
                    schema: 'public',
                    table: 'user_achievements',
                    filter: `user_id=eq.${userId}`
                }, (payload) => {
                    console.log('Achievement updated:', payload);
                    callback(payload);
                })
                .subscribe();

            return subscription;
        } catch (error) {
            console.error('Error subscribing to achievements:', error);
            return null;
        }
    }

    subscribeToNotifications(userId, callback) {
        if (this.isOffline || !this.client) {
            console.warn('Real-time subscriptions not available in offline mode');
            return null;
        }

        try {
            const subscription = this.client
                .channel(`notifications-${userId}`)
                .on('postgres_changes', {
                    event: 'INSERT',
                    schema: 'public',
                    table: 'notifications',
                    filter: `user_id=eq.${userId}`
                }, (payload) => {
                    console.log('New notification:', payload);
                    callback(payload);
                })
                .subscribe();

            return subscription;
        } catch (error) {
            console.error('Error subscribing to notifications:', error);
            return null;
        }
    }

    unsubscribe(subscription) {
        if (subscription && this.client) {
            try {
                this.client.removeChannel(subscription);
            } catch (error) {
                console.error('Error unsubscribing:', error);
            }
        }
    }

    // Enhanced Achievement System
    async checkAndUnlockAchievements(userId, gameData) {
        if (this.isOffline || !this.user) return;

        try {
            // Get user's current achievements
            const { data: userAchievements, error } = await this.client
                .from('user_achievements')
                .select('*, achievements(*)')
                .eq('user_id', userId)
                .eq('is_unlocked', false);

            if (error) throw error;

            const unlockedAchievements = [];

            for (const userAchievement of userAchievements) {
                const achievement = userAchievement.achievements;
                const criteria = achievement.unlock_criteria;
                let shouldUnlock = false;
                let newProgress = userAchievement.progress;

                // Check different achievement types
                switch (criteria.type) {
                    case 'games_won':
                        if (gameData.won) {
                            newProgress = Math.min(newProgress + 1, criteria.target);
                            shouldUnlock = newProgress >= criteria.target;
                        }
                        break;
                    case 'high_score':
                        if (gameData.score >= criteria.target) {
                            newProgress = criteria.target;
                            shouldUnlock = true;
                        }
                        break;
                    case 'time_played':
                        newProgress = Math.min(newProgress + (gameData.timePlayed || 0), criteria.target);
                        shouldUnlock = newProgress >= criteria.target;
                        break;
                    case 'games_played':
                        newProgress = Math.min(newProgress + 1, criteria.target);
                        shouldUnlock = newProgress >= criteria.target;
                        break;
                }

                // Update progress
                if (newProgress !== userAchievement.progress) {
                    await this.client
                        .from('user_achievements')
                        .update({
                            progress: newProgress,
                            is_unlocked: shouldUnlock,
                            unlocked_at: shouldUnlock ? new Date().toISOString() : null
                        })
                        .eq('id', userAchievement.id);

                    if (shouldUnlock) {
                        unlockedAchievements.push(achievement);

                        // Create notification
                        await this.createNotification(userId, {
                            type: 'achievement',
                            title: 'Achievement Unlocked!',
                            message: `You've unlocked "${achievement.name}"`,
                            data: { achievement_id: achievement.achievement_id }
                        });
                    }
                }
            }

            return unlockedAchievements;
        } catch (error) {
            console.error('Error checking achievements:', error);
            return [];
        }
    }

    async createNotification(userId, notificationData) {
        if (this.isOffline || !this.client) return;

        try {
            const { error } = await this.client
                .from('notifications')
                .insert({
                    user_id: userId,
                    ...notificationData
                });

            if (error) throw error;
        } catch (error) {
            console.error('Error creating notification:', error);
        }
    }

    async getNotifications(userId, limit = 20) {
        if (this.isOffline || !this.user) {
            return this.getFromLocalStorage('notifications', []);
        }

        try {
            const { data, error } = await this.client
                .from('notifications')
                .select('*')
                .eq('user_id', userId)
                .order('created_at', { ascending: false })
                .limit(limit);

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching notifications:', error);
            return this.getFromLocalStorage('notifications', []);
        }
    }

    async markNotificationAsRead(notificationId) {
        if (this.isOffline || !this.client) return;

        try {
            const { error } = await this.client
                .from('notifications')
                .update({
                    is_read: true,
                    read_at: new Date().toISOString()
                })
                .eq('id', notificationId);

            if (error) throw error;
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }

    // Tournament Management
    async getTournaments(active = true) {
        if (this.isOffline) {
            return this.getMockTournaments();
        }

        try {
            let query = this.client
                .from('tournaments')
                .select('*')
                .order('start_date', { ascending: true });

            if (active) {
                query = query.eq('is_active', true);
            }

            const { data, error } = await query;
            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching tournaments:', error);
            return this.getMockTournaments();
        }
    }

    async joinTournament(tournamentId) {
        if (this.isOffline || !this.user) {
            throw new Error('Tournament participation requires authentication');
        }

        try {
            const { error } = await this.client
                .from('tournament_participants')
                .insert({
                    tournament_id: tournamentId,
                    user_id: this.user.id
                });

            if (error) throw error;
            return true;
        } catch (error) {
            console.error('Error joining tournament:', error);
            throw error;
        }
    }

    getMockTournaments() {
        return [
            {
                id: '1',
                name: 'Weekly Snake Championship',
                description: 'Compete for the highest score in Snake!',
                game_id: 'snake',
                tournament_type: 'weekly',
                start_date: new Date().toISOString(),
                end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                participant_count: 42,
                is_active: true
            }
        ];
    }

    // Enhanced Error Handling
    handleError(error, context = '') {
        console.error(`Supabase error in ${context}:`, error);

        // Categorize errors
        if (error.code === 'PGRST301') {
            // Row Level Security violation
            return { type: 'permission', message: 'You do not have permission to perform this action.' };
        } else if (error.code === 'PGRST116') {
            // No rows returned
            return { type: 'not_found', message: 'The requested data was not found.' };
        } else if (error.message?.includes('network')) {
            // Network error
            return { type: 'network', message: 'Network connection error. Please check your internet connection.' };
        } else if (error.message?.includes('auth')) {
            // Authentication error
            return { type: 'auth', message: 'Authentication required. Please sign in to continue.' };
        } else {
            // Generic error
            return { type: 'generic', message: 'An unexpected error occurred. Please try again.' };
        }
    }

    // Analytics and Tracking
    async trackUserSession(sessionData) {
        if (this.isOffline || !this.user) return;

        try {
            const { error } = await this.client
                .from('user_sessions')
                .insert({
                    user_id: this.user.id,
                    session_id: sessionData.sessionId || crypto.randomUUID(),
                    device_type: this.getDeviceType(),
                    browser: this.getBrowserInfo(),
                    ...sessionData
                });

            if (error) throw error;
        } catch (error) {
            console.error('Error tracking session:', error);
        }
    }

    getDeviceType() {
        const userAgent = navigator.userAgent;
        if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
            return 'tablet';
        } else if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
            return 'mobile';
        } else {
            return 'desktop';
        }
    }

    getBrowserInfo() {
        const userAgent = navigator.userAgent;
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Safari')) return 'Safari';
        if (userAgent.includes('Edge')) return 'Edge';
        return 'Unknown';
    }

    // Getters
    isAuthenticated() {
        return !!this.user;
    }

    getCurrentUser() {
        return this.user;
    }

    getSession() {
        return this.session;
    }
}

// Initialize Supabase client
window.supabaseClient = new SupabaseClient();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SupabaseClient;
}
