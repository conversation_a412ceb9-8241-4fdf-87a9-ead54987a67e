// Homepage Functionality
class HomePage {
    constructor() {
        this.carouselIndex = 0;
        this.carouselInterval = null;
        this.userStats = this.loadUserStats();
        this.init();
    }

    init() {
        this.loadFeaturedGames();
        this.loadLatestNews();
        this.loadUserStats();
        this.setupCarousel();
        this.setupNewsletterForm();
        this.setupAnimations();
        this.updateStats();
    }

    loadFeaturedGames() {
        const container = document.getElementById('featured-games');
        if (!container) return;

        const featuredGames = getFeaturedGames();
        container.innerHTML = '';

        featuredGames.forEach((game, index) => {
            const gameCard = this.createGameCard(game, index);
            container.appendChild(gameCard);
        });

        // Initialize carousel after loading games
        this.initializeCarousel();
    }

    createGameCard(game, index) {
        const card = document.createElement('div');
        card.className = 'game-card carousel-item fade-in';
        card.style.animationDelay = `${index * 0.1}s`;
        
        card.innerHTML = `
            <div class="game-image">
                <span class="game-icon">${game.icon}</span>
                <div class="game-overlay">
                    <button class="play-btn" onclick="playGame(${game.id})" aria-label="Play ${game.title}">
                        <span class="play-icon">▶</span>
                    </button>
                </div>
                ${game.featured ? '<div class="featured-badge">Featured</div>' : ''}
            </div>
            <div class="game-info">
                <h3 class="game-title">${game.title}</h3>
                <p class="game-description">${game.description}</p>
                <div class="game-meta">
                    <div class="game-rating">
                        <span class="rating-stars">${this.generateStars(game.rating)}</span>
                        <span class="rating-value">${game.rating}</span>
                    </div>
                    <div class="game-category">${game.category}</div>
                </div>
                <div class="game-stats">
                    <span class="plays-count">
                        <span class="stat-icon">👥</span>
                        ${formatNumber(game.plays)} plays
                    </span>
                </div>
                <div class="game-actions">
                    <button class="btn btn-primary" onclick="playGame(${game.id})">
                        <span class="btn-icon">🎮</span>
                        Play Now
                    </button>
                    <button class="btn btn-secondary" onclick="showGameInfo(${game.id})">
                        <span class="btn-icon">ℹ️</span>
                        Details
                    </button>
                </div>
            </div>
        `;

        return card;
    }

    generateStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        let stars = '';

        for (let i = 0; i < fullStars; i++) {
            stars += '⭐';
        }
        if (hasHalfStar) {
            stars += '⭐';
        }
        
        return stars;
    }

    setupCarousel() {
        const prevBtn = document.getElementById('carousel-prev');
        const nextBtn = document.getElementById('carousel-next');

        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.previousSlide());
        }
        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.nextSlide());
        }

        // Auto-play carousel
        this.startCarousel();

        // Pause on hover
        const carousel = document.querySelector('.game-carousel');
        if (carousel) {
            carousel.addEventListener('mouseenter', () => this.pauseCarousel());
            carousel.addEventListener('mouseleave', () => this.startCarousel());
        }
    }

    initializeCarousel() {
        const container = document.querySelector('.games-carousel');
        if (!container) return;

        const items = container.querySelectorAll('.carousel-item');
        if (items.length === 0) return;

        // Show first 3 items initially
        items.forEach((item, index) => {
            if (index < 3) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    nextSlide() {
        const container = document.querySelector('.games-carousel');
        if (!container) return;

        const items = container.querySelectorAll('.carousel-item');
        if (items.length <= 3) return;

        this.carouselIndex = (this.carouselIndex + 1) % (items.length - 2);
        this.updateCarousel();
    }

    previousSlide() {
        const container = document.querySelector('.games-carousel');
        if (!container) return;

        const items = container.querySelectorAll('.carousel-item');
        if (items.length <= 3) return;

        this.carouselIndex = this.carouselIndex === 0 ? items.length - 3 : this.carouselIndex - 1;
        this.updateCarousel();
    }

    updateCarousel() {
        const container = document.querySelector('.games-carousel');
        if (!container) return;

        const items = container.querySelectorAll('.carousel-item');
        
        items.forEach((item, index) => {
            if (index >= this.carouselIndex && index < this.carouselIndex + 3) {
                item.style.display = 'block';
                item.style.animation = 'slideIn 0.5s ease';
            } else {
                item.style.display = 'none';
            }
        });
    }

    startCarousel() {
        this.carouselInterval = setInterval(() => {
            this.nextSlide();
        }, 5000);
    }

    pauseCarousel() {
        if (this.carouselInterval) {
            clearInterval(this.carouselInterval);
            this.carouselInterval = null;
        }
    }

    loadLatestNews() {
        const container = document.getElementById('latest-news');
        if (!container) return;

        const latestNews = getLatestNews(3);
        container.innerHTML = '';

        latestNews.forEach((article, index) => {
            const newsCard = this.createNewsCard(article, index);
            container.appendChild(newsCard);
        });
    }

    createNewsCard(article, index) {
        const card = document.createElement('div');
        card.className = 'news-card fade-in';
        card.style.animationDelay = `${index * 0.1}s`;
        
        card.innerHTML = `
            <div class="news-image">
                <span class="news-icon">${article.icon}</span>
                <div class="news-date-overlay">${this.formatDateShort(article.date)}</div>
            </div>
            <div class="news-content">
                <div class="news-category">${article.category}</div>
                <h3 class="news-title">${article.title}</h3>
                <p class="news-excerpt">${article.excerpt}</p>
                <div class="news-meta">
                    <span class="news-date">${formatDate(article.date)}</span>
                    <button class="read-more-btn" onclick="readArticle(${article.id})">
                        Read More →
                    </button>
                </div>
            </div>
        `;

        return card;
    }

    formatDateShort(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }

    setupNewsletterForm() {
        const form = document.getElementById('newsletter-form');
        if (!form) return;

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleNewsletterSubmit(form);
        });
    }

    handleNewsletterSubmit(form) {
        const email = form.querySelector('input[type="email"]').value;
        const submitBtn = form.querySelector('button[type="submit"]');
        
        if (!email) return;

        // Show loading state
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Subscribing...';
        submitBtn.disabled = true;

        // Simulate API call
        setTimeout(() => {
            // Show success message
            this.showNotification('Successfully subscribed to newsletter!', 'success');
            form.reset();
            
            // Reset button
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
            
            // Save subscription status
            localStorage.setItem('newsletter-subscribed', 'true');
        }, 1500);
    }

    loadUserStats() {
        const stats = this.getUserStats();
        
        // Update dashboard stats
        const elements = {
            'user-games-played': stats.gamesPlayed,
            'user-best-score': formatNumber(stats.bestScore),
            'user-time-played': `${Math.floor(stats.timePlayed / 60)}h`,
            'user-achievements': stats.achievements
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                this.animateCounter(element, value);
            }
        });

        // Load recently played games
        this.loadRecentlyPlayed(stats.recentlyPlayed);
    }

    getUserStats() {
        return {
            gamesPlayed: parseInt(localStorage.getItem('games-played') || '0'),
            bestScore: parseInt(localStorage.getItem('best-score') || '0'),
            timePlayed: parseInt(localStorage.getItem('time-played') || '0'), // in minutes
            achievements: parseInt(localStorage.getItem('achievements') || '0'),
            recentlyPlayed: JSON.parse(localStorage.getItem('recently-played') || '[]')
        };
    }

    animateCounter(element, targetValue) {
        const isNumeric = !isNaN(targetValue);
        if (!isNumeric) {
            element.textContent = targetValue;
            return;
        }

        const startValue = 0;
        const duration = 1000;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
            element.textContent = formatNumber(currentValue);

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    loadRecentlyPlayed(recentGames) {
        const container = document.getElementById('recently-played');
        if (!container) return;

        if (recentGames.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🎮</div>
                    <h3>No games played yet</h3>
                    <p>Start playing to see your recent games here!</p>
                    <a href="games.html" class="btn btn-primary">Browse Games</a>
                </div>
            `;
            return;
        }

        container.innerHTML = '';
        const recentGamesGrid = document.createElement('div');
        recentGamesGrid.className = 'recent-games-grid';

        recentGames.slice(0, 4).forEach((gameId, index) => {
            const game = getGameById(gameId);
            if (game) {
                const gameCard = this.createRecentGameCard(game, index);
                recentGamesGrid.appendChild(gameCard);
            }
        });

        container.appendChild(recentGamesGrid);
    }

    createRecentGameCard(game, index) {
        const card = document.createElement('div');
        card.className = 'recent-game-card slide-up';
        card.style.animationDelay = `${index * 0.1}s`;
        
        card.innerHTML = `
            <div class="recent-game-icon">${game.icon}</div>
            <div class="recent-game-info">
                <h4>${game.title}</h4>
                <p>Last played recently</p>
            </div>
            <button class="recent-play-btn" onclick="playGame(${game.id})" aria-label="Play ${game.title}">
                ▶
            </button>
        `;

        return card;
    }

    setupAnimations() {
        // Floating icons animation
        const floatingIcons = document.querySelectorAll('.float-icon');
        floatingIcons.forEach((icon, index) => {
            icon.style.animationDelay = `${index * 0.5}s`;
        });

        // Parallax effect for hero background
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.floating-shapes .shape');
            
            parallaxElements.forEach((element, index) => {
                const speed = 0.5 + (index * 0.1);
                element.style.transform = `translateY(${scrolled * speed}px)`;
            });
        });
    }

    updateStats() {
        // Update global stats with animation
        const statsElements = {
            'total-games': gamesData.length,
            'total-players': '1.2k',
            'total-scores': '50k'
        };

        Object.entries(statsElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                this.animateCounter(element, value);
            }
        });
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// Game interaction functions
function playGame(gameId) {
    // Add to recently played
    let recentlyPlayed = JSON.parse(localStorage.getItem('recently-played') || '[]');
    recentlyPlayed = recentlyPlayed.filter(id => id !== gameId);
    recentlyPlayed.unshift(gameId);
    recentlyPlayed = recentlyPlayed.slice(0, 10); // Keep only last 10
    localStorage.setItem('recently-played', JSON.stringify(recentlyPlayed));

    // Increment games played counter
    const gamesPlayed = parseInt(localStorage.getItem('games-played') || '0') + 1;
    localStorage.setItem('games-played', gamesPlayed.toString());

    // Redirect to games page with game ID
    window.location.href = `games.html?play=${gameId}`;
}

function showGameInfo(gameId) {
    window.location.href = `games.html?info=${gameId}`;
}

function readArticle(articleId) {
    window.location.href = `news.html?article=${articleId}`;
}

// Initialize homepage when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.body.contains(document.querySelector('.hero'))) {
        window.homePage = new HomePage();
    }
});
