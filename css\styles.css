/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #0f1419;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: rgba(15, 20, 25, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 1px solid #2a3441;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    color: #00d4ff;
}

.nav-logo {
    font-size: 2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: #a0a9b8;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #00d4ff;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #00d4ff;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #ffffff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Main Content */
.main-content {
    margin-top: 70px;
    min-height: calc(100vh - 70px);
}

.page {
    display: none;
    min-height: calc(100vh - 70px);
}

.page.active {
    display: block;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #1a2332 0%, #2a3441 100%);
    padding: 100px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #00d4ff, #0099cc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #a0a9b8;
    margin-bottom: 40px;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #00d4ff, #0099cc);
    color: #ffffff;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #00d4ff;
    border: 2px solid #00d4ff;
}

.btn-secondary:hover {
    background: #00d4ff;
    color: #0f1419;
    transform: translateY(-2px);
}

/* Sections */
.section {
    padding: 80px 0;
}

.section.bg-dark {
    background: #1a2332;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 50px;
    color: #ffffff;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 40px;
    color: #ffffff;
    text-align: center;
    padding: 40px 0;
}

/* Games Grid */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.game-card {
    background: #1a2332;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #2a3441;
    cursor: pointer;
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    border-color: #00d4ff;
}

.game-image {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #2a3441, #1a2332);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    position: relative;
    overflow: hidden;
}

.game-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 212, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.game-card:hover .game-image::before {
    transform: translateX(100%);
}

.game-info {
    padding: 20px;
}

.game-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #ffffff;
}

.game-description {
    color: #a0a9b8;
    font-size: 0.9rem;
    margin-bottom: 15px;
    line-height: 1.5;
}

.game-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.game-rating {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #ffd700;
}

.game-category {
    background: #00d4ff;
    color: #0f1419;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.game-actions {
    display: flex;
    gap: 10px;
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
}

.btn-play {
    background: #00d4ff;
    color: #0f1419;
    font-weight: 600;
}

.btn-play:hover {
    background: #0099cc;
    transform: translateY(-1px);
}

.btn-info {
    background: transparent;
    color: #a0a9b8;
    border: 1px solid #2a3441;
}

.btn-info:hover {
    color: #ffffff;
    border-color: #00d4ff;
}

/* Filters */
.filters {
    background: #1a2332;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 40px;
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    flex: 1;
    min-width: 250px;
}

.search-input {
    width: 100%;
    padding: 12px 16px;
    background: #0f1419;
    border: 1px solid #2a3441;
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
}

.search-input:focus {
    outline: none;
    border-color: #00d4ff;
}

.filter-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-select {
    padding: 12px 16px;
    background: #0f1419;
    border: 1px solid #2a3441;
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: #00d4ff;
}

/* Tabs */
.tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    justify-content: center;
}

.tab-btn {
    padding: 12px 24px;
    background: transparent;
    border: 1px solid #2a3441;
    border-radius: 8px;
    color: #a0a9b8;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-btn.active,
.tab-btn:hover {
    background: #00d4ff;
    color: #0f1419;
    border-color: #00d4ff;
}

/* News Categories */
.news-categories {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    justify-content: center;
    flex-wrap: wrap;
}

.category-btn {
    padding: 10px 20px;
    background: transparent;
    border: 1px solid #2a3441;
    border-radius: 20px;
    color: #a0a9b8;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.category-btn.active,
.category-btn:hover {
    background: #00d4ff;
    color: #0f1419;
    border-color: #00d4ff;
}

/* News Grid */
.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
}

.news-card {
    background: #1a2332;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #2a3441;
}

.news-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    border-color: #00d4ff;
}

.news-image {
    width: 100%;
    height: 180px;
    background: linear-gradient(135deg, #2a3441, #1a2332);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
}

.news-content {
    padding: 20px;
}

.news-category {
    background: #00d4ff;
    color: #0f1419;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 10px;
}

.news-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #ffffff;
    line-height: 1.4;
}

.news-excerpt {
    color: #a0a9b8;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 15px;
}

.news-date {
    color: #6b7280;
    font-size: 0.8rem;
}

/* Rankings */
.rankings-content {
    background: #1a2332;
    border-radius: 12px;
    overflow: hidden;
}

.ranking-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #2a3441;
    transition: background 0.3s ease;
}

.ranking-item:hover {
    background: #2a3441;
}

.ranking-item:last-child {
    border-bottom: none;
}

.ranking-position {
    font-size: 1.5rem;
    font-weight: 700;
    color: #00d4ff;
    width: 60px;
    text-align: center;
}

.ranking-position.top-3 {
    color: #ffd700;
}

.ranking-game-info {
    flex: 1;
    margin-left: 20px;
}

.ranking-game-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 5px;
}

.ranking-game-meta {
    color: #a0a9b8;
    font-size: 0.9rem;
}

.ranking-stats {
    text-align: right;
    color: #00d4ff;
    font-weight: 600;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #1a2332;
    border-radius: 12px;
    padding: 30px;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    position: relative;
    border: 1px solid #2a3441;
}

.game-play-content {
    max-width: 95%;
    max-height: 95%;
    padding: 20px;
}

.close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 2rem;
    color: #a0a9b8;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ffffff;
}

/* Game Play Modal */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #2a3441;
}

.game-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    background: #2a3441;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.control-btn:hover {
    background: #00d4ff;
    color: #0f1419;
}

.game-container {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

#game-canvas {
    border: 2px solid #2a3441;
    border-radius: 8px;
    background: #000000;
}

.game-ui {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    gap: 20px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 15px;
    border-radius: 8px;
    color: #ffffff;
    font-weight: 600;
}

.game-instructions {
    background: #2a3441;
    padding: 15px;
    border-radius: 8px;
    color: #a0a9b8;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Footer */
.footer {
    background: #1a2332;
    padding: 50px 0 20px;
    border-top: 1px solid #2a3441;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h3,
.footer-section h4 {
    color: #ffffff;
    margin-bottom: 15px;
}

.footer-section p {
    color: #a0a9b8;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: #a0a9b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #00d4ff;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #2a3441;
    color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: #0f1419;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 20px 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .games-grid {
        grid-template-columns: 1fr;
    }

    .news-grid {
        grid-template-columns: 1fr;
    }

    .filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-controls {
        justify-content: stretch;
    }

    .filter-select {
        flex: 1;
    }

    .tabs {
        flex-wrap: wrap;
    }

    .ranking-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .ranking-game-info {
        margin-left: 0;
    }

    #game-canvas {
        max-width: 100%;
        height: auto;
    }

    .modal-content {
        margin: 20px;
        max-width: calc(100% - 40px);
    }
}
