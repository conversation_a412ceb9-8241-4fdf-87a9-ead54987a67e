// Snake Game Implementation
class SnakeGame {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.gridSize = 20;
        this.tileCount = canvas.width / this.gridSize;

        // Game state
        this.snake = [{ x: 10, y: 10 }];
        this.food = {};
        this.dx = 0;
        this.dy = 0;
        this.score = 0;
        this.gameRunning = false;
        this.gamePaused = false;
        this.gameStartTime = null;
        this.lastMoveTime = 0;
        this.moveInterval = 150; // milliseconds

        // Game settings
        this.difficulty = 'normal';
        this.soundEnabled = true;
        this.highScore = parseInt(localStorage.getItem('snake-high-score') || '0');

        this.init();
    }

    init() {
        this.generateFood();
        this.setupControls();
        this.setupTouchControls();
        this.updateScore();
        this.updateHighScore();
        this.draw();
    }

    setupControls() {
        document.addEventListener('keydown', (e) => {
            if (!this.gameRunning) {
                if (e.code === 'Space') {
                    e.preventDefault();
                    this.start();
                }
                return;
            }

            if (e.code === 'Space') {
                e.preventDefault();
                this.togglePause();
                return;
            }

            if (this.gamePaused) return;

            switch(e.key) {
                case 'ArrowUp':
                case 'w':
                case 'W':
                    e.preventDefault();
                    if (this.dy !== 1) {
                        this.dx = 0;
                        this.dy = -1;
                    }
                    break;
                case 'ArrowDown':
                case 's':
                case 'S':
                    e.preventDefault();
                    if (this.dy !== -1) {
                        this.dx = 0;
                        this.dy = 1;
                    }
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    e.preventDefault();
                    if (this.dx !== 1) {
                        this.dx = -1;
                        this.dy = 0;
                    }
                    break;
                case 'ArrowRight':
                case 'd':
                case 'D':
                    e.preventDefault();
                    if (this.dx !== -1) {
                        this.dx = 1;
                        this.dy = 0;
                    }
                    break;
            }
        });
    }

    setupTouchControls() {
        let touchStartX = 0;
        let touchStartY = 0;

        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            touchStartX = touch.clientX;
            touchStartY = touch.clientY;
        });

        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            if (!this.gameRunning || this.gamePaused) return;

            const touch = e.changedTouches[0];
            const touchEndX = touch.clientX;
            const touchEndY = touch.clientY;

            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;

            const minSwipeDistance = 30;

            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                // Horizontal swipe
                if (Math.abs(deltaX) > minSwipeDistance) {
                    if (deltaX > 0 && this.dx !== -1) {
                        this.dx = 1;
                        this.dy = 0;
                    } else if (deltaX < 0 && this.dx !== 1) {
                        this.dx = -1;
                        this.dy = 0;
                    }
                }
            } else {
                // Vertical swipe
                if (Math.abs(deltaY) > minSwipeDistance) {
                    if (deltaY > 0 && this.dy !== -1) {
                        this.dx = 0;
                        this.dy = 1;
                    } else if (deltaY < 0 && this.dy !== 1) {
                        this.dx = 0;
                        this.dy = -1;
                    }
                }
            }
        });
    }

    generateFood() {
        this.food = {
            x: Math.floor(Math.random() * this.tileCount),
            y: Math.floor(Math.random() * this.tileCount)
        };

        // Make sure food doesn't spawn on snake
        for (let segment of this.snake) {
            if (segment.x === this.food.x && segment.y === this.food.y) {
                this.generateFood();
                return;
            }
        }
    }

    update() {
        if (!this.gameRunning || this.gamePaused) return;

        const head = {x: this.snake[0].x + this.dx, y: this.snake[0].y + this.dy};

        // Check wall collision
        if (head.x < 0 || head.x >= this.tileCount || head.y < 0 || head.y >= this.tileCount) {
            this.gameOver();
            return;
        }

        // Check self collision
        for (let segment of this.snake) {
            if (head.x === segment.x && head.y === segment.y) {
                this.gameOver();
                return;
            }
        }

        this.snake.unshift(head);

        // Check food collision
        if (head.x === this.food.x && head.y === this.food.y) {
            this.score += 10;
            this.updateScore();
            this.generateFood();
            this.playSound('eat');

            // Increase speed slightly
            this.moveInterval = Math.max(80, this.moveInterval - 2);
        } else {
            this.snake.pop();
        }
    }

    start() {
        if (this.gameRunning) return;

        this.snake = [{ x: 10, y: 10 }];
        this.dx = 1;
        this.dy = 0;
        this.score = 0;
        this.gameRunning = true;
        this.gamePaused = false;
        this.gameStartTime = Date.now();
        this.moveInterval = 150;

        this.generateFood();
        this.updateScore();
        this.playSound('start');
        this.gameLoop();
    }

    playSound(type) {
        if (!this.soundEnabled) return;

        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            switch (type) {
                case 'eat':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(1200, audioContext.currentTime + 0.1);
                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                    break;
                case 'gameOver':
                    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.5);
                    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                    break;
                case 'start':
                    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.2);
                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                    break;
            }

            oscillator.start();
            oscillator.stop(audioContext.currentTime + (type === 'gameOver' ? 0.5 : 0.2));
        } catch (error) {
            console.warn('Audio not supported:', error);
        }
    }

    draw() {
        // Clear canvas
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw snake
        this.ctx.fillStyle = '#00d4ff';
        for (let segment of this.snake) {
            this.ctx.fillRect(segment.x * this.gridSize, segment.y * this.gridSize, this.gridSize - 2, this.gridSize - 2);
        }

        // Draw snake head differently
        if (this.snake.length > 0) {
            this.ctx.fillStyle = '#ffffff';
            const head = this.snake[0];
            this.ctx.fillRect(head.x * this.gridSize, head.y * this.gridSize, this.gridSize - 2, this.gridSize - 2);
        }

        // Draw food
        this.ctx.fillStyle = '#ff4444';
        this.ctx.fillRect(this.food.x * this.gridSize, this.food.y * this.gridSize, this.gridSize - 2, this.gridSize - 2);

        // Draw grid lines
        this.ctx.strokeStyle = '#333333';
        this.ctx.lineWidth = 1;
        for (let i = 0; i <= this.tileCount; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(i * this.gridSize, 0);
            this.ctx.lineTo(i * this.gridSize, this.canvas.height);
            this.ctx.stroke();

            this.ctx.beginPath();
            this.ctx.moveTo(0, i * this.gridSize);
            this.ctx.lineTo(this.canvas.width, i * this.gridSize);
            this.ctx.stroke();
        }

        // Draw pause overlay
        if (this.gamePaused) {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = '48px Inter';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('PAUSED', this.canvas.width / 2, this.canvas.height / 2);
            this.ctx.font = '24px Inter';
            this.ctx.fillText('Press pause button to resume', this.canvas.width / 2, this.canvas.height / 2 + 40);
        }
    }

    gameLoop() {
        if (!this.gameRunning) return;

        const currentTime = Date.now();
        if (currentTime - this.lastMoveTime < this.moveInterval) {
            requestAnimationFrame(() => this.gameLoop());
            return;
        }

        this.lastMoveTime = currentTime;
        this.update();
        this.draw();

        if (this.gameRunning) {
            requestAnimationFrame(() => this.gameLoop());
        }
    }

    updateScore() {
        const scoreElement = document.getElementById('score');
        if (scoreElement) {
            scoreElement.textContent = this.score;
        }
    }

    updateHighScore() {
        const highScoreElement = document.getElementById('high-score');
        if (highScoreElement) {
            highScoreElement.textContent = this.highScore;
        }
    }

    gameOver() {
        this.gameRunning = false;
        this.playSound('gameOver');

        // Update high score
        if (this.score > this.highScore) {
            this.highScore = this.score;
            localStorage.setItem('snake-high-score', this.highScore.toString());
            this.updateHighScore();
        }

        // Save score
        this.saveScore();

        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        this.ctx.fillStyle = '#ff4444';
        this.ctx.font = '48px Inter';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('GAME OVER', this.canvas.width / 2, this.canvas.height / 2 - 40);

        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '24px Inter';
        this.ctx.fillText(`Final Score: ${this.score}`, this.canvas.width / 2, this.canvas.height / 2 + 10);
        this.ctx.fillText(`High Score: ${this.highScore}`, this.canvas.width / 2, this.canvas.height / 2 + 35);
        this.ctx.fillText('Press SPACE or click restart to play again', this.canvas.width / 2, this.canvas.height / 2 + 65);
    }

    saveScore() {
        const gameData = {
            score: this.score,
            timePlayed: this.gameStartTime ? Math.floor((Date.now() - this.gameStartTime) / 1000) : 0,
            moves: this.snake.length - 1,
            difficulty: this.difficulty
        };

        // Save to local storage
        const scores = JSON.parse(localStorage.getItem('snake-scores') || '[]');
        scores.push({
            ...gameData,
            date: new Date().toISOString()
        });
        scores.sort((a, b) => b.score - a.score);
        scores.splice(10); // Keep only top 10
        localStorage.setItem('snake-scores', JSON.stringify(scores));

        // Save to Supabase if available
        if (window.supabaseClient) {
            window.supabaseClient.saveGameScore('snake', this.score, gameData);
        }
    }

    togglePause() {
        this.gamePaused = !this.gamePaused;
        if (!this.gamePaused && this.gameRunning) {
            this.gameLoop();
        }
    }

    restart() {
        this.stop();
        this.start();
    }

    stop() {
        this.gameRunning = false;
        this.gamePaused = false;
    }
}
