// Main Application JavaScript

// Global variables
let currentPage = 'home';
let currentGame = null;
let gameInstance = null;

// DOM Elements
const navLinks = document.querySelectorAll('.nav-link');
const pages = document.querySelectorAll('.page');
const navToggle = document.getElementById('nav-toggle');
const navMenu = document.getElementById('nav-menu');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    loadHomePage();
    setupEventListeners();
});

// Navigation functions
function initializeNavigation() {
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.getAttribute('data-page');
            showPage(page);
        });
    });

    // Mobile menu toggle
    navToggle.addEventListener('click', function() {
        navMenu.classList.toggle('active');
    });
}

function showPage(pageName) {
    // Hide all pages
    pages.forEach(page => {
        page.classList.remove('active');
    });

    // Show selected page
    const targetPage = document.getElementById(pageName);
    if (targetPage) {
        targetPage.classList.add('active');
    }

    // Update navigation
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-page') === pageName) {
            link.classList.add('active');
        }
    });

    // Close mobile menu
    navMenu.classList.remove('active');

    // Load page content
    currentPage = pageName;
    loadPageContent(pageName);
}

function loadPageContent(pageName) {
    switch (pageName) {
        case 'home':
            loadHomePage();
            break;
        case 'games':
            loadGamesPage();
            break;
        case 'rankings':
            loadRankingsPage();
            break;
        case 'news':
            loadNewsPage();
            break;
    }
}

// Home page functions
function loadHomePage() {
    loadFeaturedGames();
    loadLatestNews();
}

function loadFeaturedGames() {
    const container = document.getElementById('featured-games');
    const featuredGames = getFeaturedGames();
    
    container.innerHTML = '';
    featuredGames.forEach(game => {
        container.appendChild(createGameCard(game));
    });
}

function loadLatestNews() {
    const container = document.getElementById('latest-news');
    const latestNews = getLatestNews(3);
    
    container.innerHTML = '';
    latestNews.forEach(article => {
        container.appendChild(createNewsCard(article));
    });
}

// Games page functions
function loadGamesPage() {
    loadAllGames();
    setupGameFilters();
}

function loadAllGames() {
    const container = document.getElementById('all-games');
    container.innerHTML = '';
    
    gamesData.forEach(game => {
        container.appendChild(createGameCard(game));
    });
}

function setupGameFilters() {
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');
    const sortFilter = document.getElementById('sort-filter');

    searchInput.addEventListener('input', filterGames);
    categoryFilter.addEventListener('change', filterGames);
    sortFilter.addEventListener('change', filterGames);
}

function filterGames() {
    const searchQuery = document.getElementById('search-input').value;
    const category = document.getElementById('category-filter').value;
    const sortBy = document.getElementById('sort-filter').value;

    let filteredGames = getGamesByCategory(category);
    
    if (searchQuery) {
        filteredGames = searchGames(searchQuery);
        if (category !== 'all') {
            filteredGames = filteredGames.filter(game => game.category === category);
        }
    }

    filteredGames = sortGames(filteredGames, sortBy);

    const container = document.getElementById('all-games');
    container.innerHTML = '';
    
    filteredGames.forEach(game => {
        container.appendChild(createGameCard(game));
    });
}

// Rankings page functions
function loadRankingsPage() {
    setupRankingTabs();
    loadTopRatedGames();
}

function setupRankingTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            tabBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const tab = this.getAttribute('data-tab');
            if (tab === 'top-rated') {
                loadTopRatedGames();
            } else if (tab === 'most-played') {
                loadMostPlayedGames();
            }
        });
    });
}

function loadTopRatedGames() {
    const container = document.getElementById('rankings-content');
    const topRated = getTopRatedGames();
    
    container.innerHTML = '';
    topRated.forEach((game, index) => {
        container.appendChild(createRankingItem(game, index + 1, 'rating'));
    });
}

function loadMostPlayedGames() {
    const container = document.getElementById('rankings-content');
    const mostPlayed = getMostPlayedGames();
    
    container.innerHTML = '';
    mostPlayed.forEach((game, index) => {
        container.appendChild(createRankingItem(game, index + 1, 'plays'));
    });
}

// News page functions
function loadNewsPage() {
    setupNewsCategories();
    loadAllNews();
}

function setupNewsCategories() {
    const categoryBtns = document.querySelectorAll('.category-btn');
    
    categoryBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            categoryBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const category = this.getAttribute('data-category');
            loadNewsByCategory(category);
        });
    });
}

function loadAllNews() {
    loadNewsByCategory('all');
}

function loadNewsByCategory(category) {
    const container = document.getElementById('news-content');
    const news = getNewsByCategory(category);
    
    container.innerHTML = '';
    news.forEach(article => {
        container.appendChild(createNewsCard(article));
    });
}

// Card creation functions
function createGameCard(game) {
    const card = document.createElement('div');
    card.className = 'game-card';
    card.innerHTML = `
        <div class="game-image">
            <span style="font-size: 4rem;">${game.icon}</span>
        </div>
        <div class="game-info">
            <h3 class="game-title">${game.title}</h3>
            <p class="game-description">${game.description}</p>
            <div class="game-meta">
                <div class="game-rating">
                    <span>⭐</span>
                    <span>${game.rating}</span>
                </div>
                <div class="game-category">${game.category}</div>
            </div>
            <div class="game-actions">
                <button class="btn-small btn-play" onclick="playGame(${game.id})">Play</button>
                <button class="btn-small btn-info" onclick="showGameInfo(${game.id})">Info</button>
            </div>
        </div>
    `;
    return card;
}

function createNewsCard(article) {
    const card = document.createElement('div');
    card.className = 'news-card';
    card.innerHTML = `
        <div class="news-image">
            <span style="font-size: 3rem;">${article.icon}</span>
        </div>
        <div class="news-content">
            <div class="news-category">${article.category}</div>
            <h3 class="news-title">${article.title}</h3>
            <p class="news-excerpt">${article.excerpt}</p>
            <div class="news-date">${formatDate(article.date)}</div>
        </div>
    `;
    return card;
}

function createRankingItem(game, position, type) {
    const item = document.createElement('div');
    item.className = 'ranking-item';
    
    const positionClass = position <= 3 ? 'ranking-position top-3' : 'ranking-position';
    const statValue = type === 'rating' ? game.rating : formatNumber(game.plays);
    const statLabel = type === 'rating' ? 'Rating' : 'Plays';
    
    item.innerHTML = `
        <div class="${positionClass}">${position}</div>
        <div class="ranking-game-info">
            <div class="ranking-game-title">${game.title}</div>
            <div class="ranking-game-meta">${game.category} • ${game.icon}</div>
        </div>
        <div class="ranking-stats">
            <div>${statValue}</div>
            <div style="font-size: 0.8rem; color: #a0a9b8;">${statLabel}</div>
        </div>
    `;
    
    item.addEventListener('click', () => showGameInfo(game.id));
    return item;
}

// Event listeners setup
function setupEventListeners() {
    // Close modals when clicking outside
    window.addEventListener('click', function(e) {
        const gameModal = document.getElementById('game-modal');
        const gamePlayModal = document.getElementById('game-play-modal');
        
        if (e.target === gameModal) {
            closeGameModal();
        }
        if (e.target === gamePlayModal) {
            closeGamePlayModal();
        }
    });

    // Game control buttons
    document.getElementById('pause-btn').addEventListener('click', togglePause);
    document.getElementById('restart-btn').addEventListener('click', restartGame);
}

// Game functions (to be implemented with actual games)
function playGame(gameId) {
    const game = getGameById(gameId);
    if (!game) return;

    currentGame = game;
    document.getElementById('game-play-title').textContent = game.title;
    document.getElementById('game-play-modal').classList.add('active');
    
    // Initialize the specific game
    initializeGame(game.gameType);
}

function showGameInfo(gameId) {
    const game = getGameById(gameId);
    if (!game) return;

    const modal = document.getElementById('game-modal');
    const content = document.getElementById('game-detail-content');
    
    content.innerHTML = `
        <div style="text-align: center; margin-bottom: 20px;">
            <div style="font-size: 5rem; margin-bottom: 10px;">${game.icon}</div>
            <h2 style="color: #ffffff; margin-bottom: 10px;">${game.title}</h2>
            <div style="color: #00d4ff; margin-bottom: 20px;">${game.category}</div>
        </div>
        <p style="color: #a0a9b8; line-height: 1.6; margin-bottom: 20px;">${game.description}</p>
        <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
            <div>
                <strong style="color: #ffffff;">Rating:</strong>
                <span style="color: #ffd700;">⭐ ${game.rating}</span>
            </div>
            <div>
                <strong style="color: #ffffff;">Plays:</strong>
                <span style="color: #00d4ff;">${formatNumber(game.plays)}</span>
            </div>
        </div>
        <div style="text-align: center;">
            <button class="btn btn-primary" onclick="closeGameModal(); playGame(${game.id});">Play Now</button>
        </div>
    `;
    
    modal.classList.add('active');
}

function closeGameModal() {
    document.getElementById('game-modal').classList.remove('active');
}

function closeGamePlayModal() {
    document.getElementById('game-play-modal').classList.remove('active');
    if (gameInstance && gameInstance.stop) {
        gameInstance.stop();
    }
    gameInstance = null;
}

function togglePause() {
    if (gameInstance && gameInstance.togglePause) {
        gameInstance.togglePause();
    }
}

function restartGame() {
    if (gameInstance && gameInstance.restart) {
        gameInstance.restart();
    }
}

function initializeGame(gameType) {
    const canvas = document.getElementById('game-canvas');
    const instructions = document.getElementById('game-instructions');
    
    // Clear previous game
    if (gameInstance && gameInstance.stop) {
        gameInstance.stop();
    }
    
    // Initialize specific game based on type
    switch (gameType) {
        case 'snake':
            if (typeof SnakeGame !== 'undefined') {
                gameInstance = new SnakeGame(canvas);
                instructions.innerHTML = `
                    <strong>How to Play:</strong><br>
                    • Use arrow keys or WASD to move the snake<br>
                    • Eat food (red squares) to grow longer<br>
                    • Avoid hitting walls or your own tail<br>
                    • Try to get the highest score possible!
                `;
            }
            break;
        case 'tetris':
            if (typeof TetrisGame !== 'undefined') {
                gameInstance = new TetrisGame(canvas);
                instructions.innerHTML = `
                    <strong>How to Play:</strong><br>
                    • Use arrow keys to move and rotate pieces<br>
                    • Down arrow to drop pieces faster<br>
                    • Complete horizontal lines to clear them<br>
                    • Game ends when pieces reach the top!
                `;
            }
            break;
        case 'pong':
            if (typeof PongGame !== 'undefined') {
                gameInstance = new PongGame(canvas);
                instructions.innerHTML = `
                    <strong>How to Play:</strong><br>
                    • Use Up/Down arrow keys to move your paddle<br>
                    • Hit the ball back to your opponent<br>
                    • First to score 10 points wins!<br>
                    • Ball speed increases over time
                `;
            }
            break;
        default:
            instructions.innerHTML = `
                <strong>Game not yet implemented!</strong><br>
                This game is coming soon. Check back later for updates!
            `;
            break;
    }
}
