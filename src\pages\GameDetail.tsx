import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Star, Users, Play, Share, Heart, Calendar } from 'lucide-react';
import { games } from '../data/games';

const GameDetail = () => {
  const { id } = useParams();
  const game = games.find(g => g.id === parseInt(id || '0'));

  if (!game) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Game Not Found</h1>
          <Link to="/games" className="text-blue-400 hover:text-blue-300">
            ← Back to Games
          </Link>
        </div>
      </div>
    );
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'hard': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <nav className="mb-8">
          <Link to="/games" className="text-blue-400 hover:text-blue-300">
            ← Back to Games
          </Link>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Game Image and Actions */}
          <div className="lg:col-span-1">
            <div className="bg-gray-800 rounded-lg overflow-hidden shadow-lg">
              <img
                src={`https://picsum.photos/600/400?random=${game.id}`}
                alt={game.title}
                className="w-full h-64 object-cover"
              />
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <Star className="h-5 w-5 text-yellow-400 fill-current" />
                    <span className="text-lg font-semibold text-white">{game.rating}</span>
                    <span className="text-gray-400">/ 5.0</span>
                  </div>
                  <div className={`${getDifficultyColor(game.difficulty)} text-white px-3 py-1 rounded-full text-sm font-semibold`}>
                    {game.difficulty}
                  </div>
                </div>

                <div className="space-y-4">
                  <Link
                    to={`/play/${game.id}`}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors"
                  >
                    <Play className="h-5 w-5" />
                    <span>Play Now</span>
                  </Link>

                  <div className="grid grid-cols-2 gap-2">
                    <button className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors">
                      <Heart className="h-4 w-4" />
                      <span>Favorite</span>
                    </button>
                    <button className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors">
                      <Share className="h-4 w-4" />
                      <span>Share</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Game Stats */}
            <div className="bg-gray-800 rounded-lg p-6 mt-6">
              <h3 className="text-lg font-semibold text-white mb-4">Game Statistics</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Plays</span>
                  <span className="text-white font-semibold">{game.plays.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Category</span>
                  <span className="text-blue-400">{game.category}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Difficulty</span>
                  <span className="text-white">{game.difficulty}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Rating</span>
                  <span className="text-yellow-400">★ {game.rating}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Game Details */}
          <div className="lg:col-span-2">
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="flex items-start justify-between mb-6">
                <div>
                  <h1 className="text-3xl font-bold text-white mb-2">{game.title}</h1>
                  <div className="flex items-center space-x-4 text-sm text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>{game.plays.toLocaleString()} plays</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>Updated recently</span>
                    </div>
                  </div>
                </div>
                {game.featured && (
                  <div className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Featured
                  </div>
                )}
              </div>

              {/* Description */}
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-white mb-3">About This Game</h2>
                <p className="text-gray-300 leading-relaxed">
                  {game.description}. Experience hours of entertainment with this carefully crafted game 
                  that combines classic gameplay mechanics with modern design. Perfect for players of all 
                  skill levels, this game offers intuitive controls and engaging challenges that will 
                  keep you coming back for more.
                </p>
              </div>

              {/* Tags */}
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-white mb-3">Tags</h2>
                <div className="flex flex-wrap gap-2">
                  {game.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-gray-700 text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-gray-600 transition-colors cursor-pointer"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              {/* How to Play */}
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-white mb-3">How to Play</h2>
                <div className="bg-gray-700 rounded-lg p-4">
                  <ul className="text-gray-300 space-y-2">
                    <li>• Use arrow keys or WASD to move your character</li>
                    <li>• Collect items to increase your score</li>
                    <li>• Avoid obstacles and enemies</li>
                    <li>• Try to achieve the highest score possible</li>
                    <li>• Press ESC to pause the game</li>
                  </ul>
                </div>
              </div>

              {/* Similar Games */}
              <div>
                <h2 className="text-xl font-semibold text-white mb-3">Similar Games</h2>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {games
                    .filter(g => g.category === game.category && g.id !== game.id)
                    .slice(0, 4)
                    .map((similarGame) => (
                      <Link
                        key={similarGame.id}
                        to={`/game/${similarGame.id}`}
                        className="bg-gray-700 rounded-lg overflow-hidden hover:bg-gray-600 transition-colors"
                      >
                        <img
                          src={`https://picsum.photos/200/120?random=${similarGame.id}`}
                          alt={similarGame.title}
                          className="w-full h-20 object-cover"
                        />
                        <div className="p-2">
                          <h4 className="text-white text-sm font-medium truncate">
                            {similarGame.title}
                          </h4>
                          <div className="flex items-center space-x-1 mt-1">
                            <Star className="h-3 w-3 text-yellow-400 fill-current" />
                            <span className="text-xs text-gray-400">{similarGame.rating}</span>
                          </div>
                        </div>
                      </Link>
                    ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameDetail;
